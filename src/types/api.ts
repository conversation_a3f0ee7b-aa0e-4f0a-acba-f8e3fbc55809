import { type ChatArgs } from './chatArgs';
import { type UserInfo } from './user';
import { type ButtonPosition } from './ui';
import { type ChatbotMessage } from './message';
import { type VisibilitySettings, type SessionOptions } from './session';

// Global window interface extension
declare global {
  interface Window {
    ChatbotWidget: {
      push: (instruction: ChatbotInstruction) => void;
      initialize?: (params?: ChatArgs) => void;
    };
    initializeDirectLineConnection?: () => void;
    openChatbot?: () => void;
    closeChatbot?: () => void;
    setChatbotUserInfo?: (userInfo: Record<string, unknown>) => void;
    setChatbotTopic?: (topic: string, message?: string) => void;
    setChatbotPosition?: (position: string) => void;
    clearChatbotMessages?: () => void;
    destroyChatbot?: () => void;
    setChatbotSessionData?: (sessionData: Record<string, unknown>) => void;
    getChatbotSessionData?: () => Record<string, unknown>;
    clearChatbotSession?: () => void;
  }
}

// Event callback types - clean interface-based approach
export interface ChatbotEventMap {
  'widget.initialized': (state: ChatbotState) => void;
  'widget.opened': (state: ChatbotState) => void;
  'widget.closed': (state: ChatbotState) => void;
  'session.created': (sessionInfo: { sessionId: string; userInfo?: UserInfo }) => void;
  'message.sent': (message: ChatbotMessage) => void;
  'message.received': (message: ChatbotMessage) => void;
  'variable.set': (data: { key: string; value: unknown }) => void;
  'typing.start': () => void;
  'typing.stop': () => void;
  'theme.changed': (theme: string) => void;
  'error': (error: ChatbotError) => void;
}

export type ChatbotCallbackTypeFor<E extends keyof ChatbotEventMap> = ChatbotEventMap[E];

// Chatbot state interface
export interface ChatbotState {
  isOpen: boolean;
  isInitialized: boolean;
  hasMessages: boolean;
  messageCount: number;
  currentTheme: string;
  position: ButtonPosition;
  sessionId?: string;
}

// Error interface
export interface ChatbotError {
  code: string;
  message: string;
  details?: unknown;
}

// Instruction types
export type ChatbotInstruction = 
  | InitInstruction
  | OpenInstruction
  | CloseInstruction
  | SendMessageInstruction
  | SetVariableInstruction
  | SetUserInfoInstruction
  | SetTopicInstruction
  | OnEventInstruction
  | OffEventInstruction
  | ClearMessagesInstruction
  | DestroyInstruction
  | GetStateInstruction
  | SetSessionDataInstruction
  | GetSessionDataInstruction
  | ClearSessionInstruction
  | InitializeConnectionInstruction
  | InitializeSessionInstruction;

// Individual instruction interfaces
export interface InitInstruction {
  action: 'init';
  args?: ChatArgs;
}

export interface OpenInstruction {
  action: 'open';
}

export interface CloseInstruction {
  action: 'close';
}

export interface SendMessageInstruction {
  action: 'sendMessage';
  args: {
    text: string;
    metadata?: Record<string, unknown>;
  };
}

export interface SetVariableInstruction {
  action: 'setVariable';
  args: {
    key: string;
    value: unknown;
  };
}

export interface SetUserInfoInstruction {
  action: 'setUserInfo';
  args: {
    userInfo: UserInfo;
  };
}

export interface SetTopicInstruction {
  action: 'setTopic';
  args: {
    topic: string;
    message?: string;
  };
}

export interface OnEventInstruction {
  action: 'on';
  args: {
    event: keyof ChatbotEventMap;
    callback: ChatbotCallbackTypeFor<keyof ChatbotEventMap>;
  };
}

export interface OffEventInstruction {
  action: 'off';
  args: {
    event: string;
  };
}

export interface ClearMessagesInstruction {
  action: 'clearMessages';
}

export interface DestroyInstruction {
  action: 'destroy';
}

export interface GetStateInstruction {
  action: 'getState';
  callback: (state: ChatbotState) => void;
}

export interface SetSessionDataInstruction {
  action: 'setSessionData';
  args: {
    visibility?: VisibilitySettings;
    options?: SessionOptions;
    accessToken?: string;
    conversationUrl?: string;
    customData?: Record<string, unknown>;
  };
}

export interface GetSessionDataInstruction {
  action: 'getSessionData';
  callback: (sessionData: {
    visibility?: VisibilitySettings;
    options?: SessionOptions;
    accessToken?: string;
    conversationUrl?: string;
    customData?: Record<string, unknown>;
  }) => void;
}

export interface ClearSessionInstruction {
  action: 'clearSession';
}

export interface InitializeConnectionInstruction {
  action: 'initializeConnection';
}

export interface InitializeSessionInstruction {
  action: 'initializeSession';
}

// Callback tuple type
export type ChatbotCallbackTuple<E extends keyof ChatbotEventMap = keyof ChatbotEventMap> = [E, ChatbotCallbackTypeFor<E>];