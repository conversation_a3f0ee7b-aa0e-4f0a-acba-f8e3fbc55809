import { type Message } from './message';
import { type ChatbotContext, type Visibility } from './chatArgs';

export interface VisibilitySettings {
  window?: Visibility;
  inline?: 'visible' | 'hidden';
}

export interface SessionOptions {
  [key: string]: unknown;
}

export interface ChatbotSession {
  id: string;
  authToken?: string;
  context?: ChatbotContext;
  topic?: string;
  messages: Message[];
  createdAt: Date;
  
  // Enhanced session data for Bot Framework integration
  visibility?: VisibilitySettings;
  options?: SessionOptions;
  accessToken?: string;
  conversationUrl?: string;
  conversationId?: string;
  conversationToken?: string;
  userId?: string;
  
  // Flag to track if session was loaded from localStorage (existing) vs created new
  isFromLocalStorage?: boolean;
  
  // Custom session storage for any additional data
  customData?: Record<string, unknown>;
}