export type Namespace = 'green-namespace' | 'blue-namespace' | 'purple-namespace' | 'light-namespace' | 'dark-namespace';
export type LayoutMode = 'window' | 'inline' | null;
export type Visibility = 'open' | 'minimized' | 'hidden' | null;

export interface ChatbotContext {
  contentLocale?: string;
  userLocale?: string;
  variables?: {
    name?: string;
    event?: string;
  };
}

export interface ChatbotAPI {
  getConversationAuthToken: () => string | undefined;
}

export interface ProxyConfig {
  proxyUrl: string;
  apiKey?: string;
  debug?: boolean;
}

export type ChatArgs = {
  // Namespace-based theming
  namespace?: Namespace;
  layoutMode?: LayoutMode;
  visibility?: Visibility;
  parentElement?: HTMLDivElement;
  
  // Core functionality
  topic?: string;
  askText?: string;
  sendTopicAndAskText?: boolean;
  fallbackMessage?: string;
  hideOnNoUserResponse?: boolean;
  accountId?: string;
  authToken?: string;
  context?: ChatbotContext;
  api?: ChatbotAPI;
  
  // Proxy configuration for secure DirectLine
  proxyConfig?: ProxyConfig;
};