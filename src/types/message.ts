export interface SuggestedAction {
  type: string;
  title: string;
  text: string;
  value: string;
}

export interface SuggestedActions {
  to: string[];
  actions: SuggestedAction[];
}

export interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date | string;
  textFormat?: 'plain' | 'markdown';
  suggestedActions?: SuggestedActions;
}

export interface ChatbotMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date | string;
  textFormat?: 'plain' | 'markdown';
  metadata?: Record<string, unknown>;
  suggestedActions?: SuggestedActions;
}