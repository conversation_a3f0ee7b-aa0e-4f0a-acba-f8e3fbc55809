export interface DirectLineChannelAccount {
  id: string;
  name?: string;
  role?: string;
}

export interface DirectLineConversationAccount {
  id: string;
}

export interface DirectLineAttachment {
  contentType: string;
  content?: any;
  contentUrl?: string;
  name?: string;
  thumbnailUrl?: string;
}

export interface DirectLineChannelData {
  feedbackLoop?: {
    type: string;
  };
  [key: string]: any;
}

export interface DirectLineActivity {
  type: 'message' | 'typing' | 'event' | 'conversationUpdate' | 'contactRelationUpdate' | 'deleteUserData';
  id?: string;
  timestamp?: string;
  localTimestamp?: string;
  serviceUrl?: string;
  channelId?: string;
  from: DirectLineChannelAccount;
  conversation?: DirectLineConversationAccount;
  recipient?: DirectLineChannelAccount;
  textFormat?: 'plain' | 'markdown';
  text?: string;
  speak?: string;
  inputHint?: 'acceptingInput' | 'ignoringInput' | 'expectingInput';
  summary?: string;
  suggestedActions?: {
    actions: {
      type: string;
      title: string;
      value: any;
    }[];
  };
  attachments?: DirectLineAttachment[];
  entities?: any[];
  channelData?: DirectLineChannelData;
  action?: string;
  replyToId?: string;
  label?: string;
  valueType?: string;
  value?: any;
  name?: string;
  membersAdded?: DirectLineChannelAccount[];
  membersRemoved?: DirectLineChannelAccount[];
  reactionsAdded?: any[];
  reactionsRemoved?: any[];
  listenFor?: string[];
  textHighlights?: any[];
}

export interface DirectLineActivitySet {
  activities: DirectLineActivity[];
  watermark: string;
}