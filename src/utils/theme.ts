/**
 * Theme-related utility functions
 */

/**
 * Converts namespace to theme name for compatibility
 * @param namespace - Namespace string from ChatArgs
 * @returns Theme name string
 */
export const getThemeFromNamespace = (namespace?: string): string => {
  switch (namespace) {
    case 'dark-namespace': return 'dark';
    case 'blue-namespace': return 'blue';
    case 'green-namespace': return 'green';
    case 'purple-namespace': return 'purple';
    default: return 'light';
  }
};