/**
 * Session-related utility functions
 */

/**
 * Checks if there's an existing session in localStorage
 * @returns Boolean indicating if an existing session exists
 */
export const checkForExistingSession = (): boolean => {
  try {
    const sessionData = localStorage.getItem('chatbot-session');
    if (sessionData) {
      const parsed = JSON.parse(sessionData);
      return !!(parsed.accessToken && parsed.conversationUrl);
    }
  } catch (error) {
    console.error('Error checking existing session:', error);
  }
  return false;
};