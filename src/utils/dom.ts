/**
 * DOM-related utility functions
 */

/**
 * Scrolls an element into view with smooth behavior
 * @param element - HTML element to scroll to
 */
export const scrollToBottom = (element: HTMLDivElement | null) => {
  element?.scrollIntoView({ behavior: 'smooth' });
};

/**
 * Gets overlay position styles for widget positioning
 * @param position - Position string (e.g., 'right-bottom')
 * @returns CSS properties object for positioning
 */
export const getOverlayPositionStyle = (position: string): React.CSSProperties => {
  const styles: React.CSSProperties = {
    position: 'fixed',
    zIndex: 10000,
  };

  switch (position) {
    case 'left-bottom':
      styles.bottom = '90px';
      styles.left = '20px';
      break;
    case 'right-top':
      styles.top = '90px';
      styles.right = '20px';
      break;
    case 'left-top':
      styles.top = '90px';
      styles.left = '20px';
      break;
    case 'right-bottom':
    default:
      styles.bottom = '90px';
      styles.right = '20px';
      break;
  }

  return styles;
};