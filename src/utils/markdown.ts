/**
 * Markdown-related utility functions
 */

import React from 'react';

/**
 * Simple markdown link parser that converts [text](url) to Link components
 * @param text - Text that may contain markdown links
 * @param LinkComponent - Link component to render
 * @returns Array of React nodes (text and Link components)
 */
export const parseMarkdownLinks = (
  text: string, 
  LinkComponent: React.ComponentType<{ href: string; children: string }>
): (string | React.ReactElement)[] => {
  const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  const parts: (string | React.ReactElement)[] = [];
  let lastIndex = 0;
  let match;

  while ((match = linkRegex.exec(text)) !== null) {
    // Add text before the link
    if (match.index > lastIndex) {
      parts.push(text.slice(lastIndex, match.index));
    }

    // Add the link component
    const linkText = match[1];
    const linkUrl = match[2];
    parts.push(
      React.createElement(LinkComponent, {
        key: `link-${match.index}`,
        href: linkUrl,
        children: linkText
      })
    );

    lastIndex = match.index + match[0].length;
  }

  // Add remaining text after the last link
  if (lastIndex < text.length) {
    parts.push(text.slice(lastIndex));
  }

  return parts.length > 0 ? parts : [text];
};