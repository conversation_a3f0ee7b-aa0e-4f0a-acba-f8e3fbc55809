/**
 * Time-related utility functions
 */

/**
 * Formats a date or string timestamp to HH:MM format
 * @param date - Date object or string timestamp
 * @returns Formatted time string in HH:MM format
 */
export const formatTime = (date: Date | string): string => {
  try {
    // Convert to Date object if it's a string
    const dateObj = date instanceof Date ? date : new Date(date);
    
    // Validate the Date object
    if (isNaN(dateObj.getTime())) {
      return new Date().toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    }
    
    return dateObj.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  } catch {
    console.warn('Invalid timestamp, using current time:', date);
    return new Date().toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  }
};