/**
 * UI-related utility functions
 */

import { type ChatArgs } from '../types/chatArgs';
import { checkForExistingSession } from './session';

/**
 * Creates a floating button element outside Shadow DOM for proper positioning
 * @param params - ChatArgs configuration parameters
 * @param colors - Theme colors object
 * @param existingButtonElement - Reference to existing button element (for replacement)
 * @param onClickHandler - Click handler function
 * @returns HTMLButtonElement
 */
export const createFloatingButton = (
  _params: ChatArgs, 
  colors: { [key: string]: string },
  existingButtonElement: HTMLElement | null,
  onClickHandler: () => void
): HTMLButtonElement => {
  // Remove existing button if any
  if (existingButtonElement && existingButtonElement.parentNode) {
    existingButtonElement.parentNode.removeChild(existingButtonElement);
  }

  // Check if existing session exists for indicator
  const hasExistingSession = checkForExistingSession();

  // Create button element
  const buttonElement = document.createElement('button');
  buttonElement.className = 'chatbot-trigger-button';
  
  if (hasExistingSession) {
    buttonElement.innerHTML = '💬<span style="position: absolute; top: -2px; right: -2px; width: 12px; height: 12px; background: #00ff00; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 0 1px rgba(0,0,0,0.1);"></span>';
    buttonElement.title = 'Continue Conversation';
  } else {
    buttonElement.innerHTML = '💬';
    buttonElement.title = 'Start Chat';
  }

  // Apply positioning styles
  const styles: { [key: string]: string } = {
    position: 'fixed',
    width: '60px',
    height: '60px',
    borderRadius: '50%',
    border: 'none',
    backgroundColor: colors.primary,
    color: colors.textOnPrimary,
    fontSize: '24px',
    cursor: 'pointer',
    boxShadow: `0 4px 20px ${colors.shadow}`,
    zIndex: '10001',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: '0.3s ease',
    // Default positioning
    bottom: '24px',
    right: '24px'
  };

  // Apply styles
  Object.assign(buttonElement.style, styles);

  // Add hover effects
  buttonElement.addEventListener('mouseenter', () => {
    buttonElement.style.backgroundColor = colors.primaryHover;
    buttonElement.style.transform = 'scale(1.05)';
  });

  buttonElement.addEventListener('mouseleave', () => {
    buttonElement.style.backgroundColor = colors.primary;
    buttonElement.style.transform = 'scale(1)';
  });

  // Add click handler
  buttonElement.addEventListener('click', onClickHandler);

  // Add to document body
  document.body.appendChild(buttonElement);
  
  return buttonElement;
};