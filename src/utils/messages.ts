/**
 * Message-related utility functions
 */

import { type Message } from '../types/message';

/**
 * Finds the index of the latest bot message in an array of messages
 * @param messages - Array of messages to search
 * @returns Index of the latest bot message, or -1 if none found
 */
export const findLatestBotMessageIndex = (messages: Message[]): number => {
  for (let i = messages.length - 1; i >= 0; i--) {
    if (!messages[i].isUser) {
      return i;
    }
  }
  return -1;
};