import { createContext } from 'react';
import { type Message, type ChatbotSession, type ChatArgs } from '../types';

export interface ChatbotContextType {
  messages: Message[];
  isTyping: boolean;
  session: ChatbotSession | null;
  isInitialized: boolean;
  isConnected: boolean;
  sendMessage: (text: string) => Promise<void>;
  clearMessages: () => void;
  initializeSession: (params: ChatArgs) => void;
  initializeDirectLineConnection: () => void;
}

export const ChatbotContext = createContext<ChatbotContextType | undefined>(undefined);