import { useState, useCallback, type ReactNode } from 'react';
import { type Message, type ChatbotSession, type ChatArgs } from '../types';
import { ChatbotContext, type ChatbotContextType } from './chatbotContextDefinition';
import { SessionManager } from '../services';
import { useMessageHandler, type ProxyDirectLineConfig } from '../hooks/useMessageHandler';

interface ProxyBotFrameworkProviderProps {
  children: ReactNode;
  proxyConfig: ProxyDirectLineConfig;
}

export function ChatbotProvider({ 
  children, 
  proxyConfig 
}: ProxyBotFrameworkProviderProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [session, setSession] = useState<ChatbotSession | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  const sessionManager = SessionManager.getInstance();
  
  const { sendMessage, clearMessages, initializeDirectLine, isConnected } = useMessageHandler({
    session,
    setIsTyping,
    setMessages,
    setSession,
    sessionManager,
    proxyConfig
  });

  // Function to initialize DirectLine when chat is opened
  const initializeDirectLineConnection = useCallback(() => {
    console.log('🚀 Initializing DirectLine connection after chat button click...');
    initializeDirectLine();
  }, [initializeDirectLine]);


  const initializeSession = useCallback((params: ChatArgs) => {
    if (isInitialized) {
      console.log('⚠️ Session already initialized, skipping...');
      return;
    }

    console.log('🔧 Initializing Proxy Bot Framework session...');
    
    let newSession: ChatbotSession;

    // First check if there's a valid existing session in localStorage
    if (sessionManager.hasValidSession()) {
      const existingSession = sessionManager.getCurrentSession()!;
      console.log('🔗 Found existing session, attempting to reuse...', {
        conversationId: existingSession.conversationId,
        hasAccessToken: !!existingSession.accessToken,
        hasConversationUrl: !!existingSession.conversationUrl
      });
      
      // Always reuse existing session if valid (contains DirectLine data)
      newSession = existingSession;
    } else {
      console.log('🆕 No existing session found, creating new one...');
      // Create new session
      newSession = sessionManager.createSession(params);
    }
    
    setSession(newSession);
    setMessages(newSession.messages);
    setIsInitialized(true);

    // Don't initialize DirectLine connection here - wait for chat button click
    
  }, [sessionManager, initializeDirectLine, isInitialized]);

  const contextValue: ChatbotContextType = {
    messages,
    isTyping,
    session,
    isInitialized,
    isConnected,
    sendMessage,
    clearMessages,
    initializeSession,
    initializeDirectLineConnection,
  };

  return (
    <ChatbotContext.Provider value={contextValue}>
      {children}
    </ChatbotContext.Provider>
  );
}