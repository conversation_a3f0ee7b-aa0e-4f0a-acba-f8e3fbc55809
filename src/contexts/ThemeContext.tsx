import { useState, useEffect, type ReactNode } from 'react';
import { type Theme, type ThemeName, getTheme } from '../theme';
import { ThemeContext, type ThemeContextType } from './themeContextDefinition';

interface ThemeProviderProps {
  children: ReactNode;
  defaultThemeName?: ThemeName;
  storageKey?: string;
}

export function ThemeProvider({ 
  children, 
  defaultThemeName = 'light',
  storageKey = 'chatbot-theme'
}: ThemeProviderProps) {
  const [themeName, setThemeName] = useState<ThemeName>(defaultThemeName);
  const [currentTheme, setCurrentTheme] = useState<Theme>(getTheme(defaultThemeName));

  // Load theme from localStorage on mount
  useEffect(() => {
    if (typeof localStorage !== 'undefined') {
      try {
        const savedTheme = localStorage.getItem(storageKey) as ThemeName;
        if (savedTheme && getTheme(savedTheme)) {
          setThemeName(savedTheme);
          setCurrentTheme(getTheme(savedTheme));
        }
      } catch (error) {
        console.warn('Failed to load theme from localStorage:', error);
      }
    }
  }, [storageKey]);

  // Update theme
  const setTheme = (newThemeName: ThemeName) => {
    const theme = getTheme(newThemeName);
    setThemeName(newThemeName);
    setCurrentTheme(theme);
    
    // Save to localStorage
    if (typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(storageKey, newThemeName);
      } catch (error) {
        console.warn('Failed to save theme to localStorage:', error);
      }
    }
  };

  // Toggle between light and dark themes
  const toggleTheme = () => {
    const newTheme = currentTheme.variant === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
  };

  const contextValue: ThemeContextType = {
    currentTheme,
    themeName,
    setTheme,
    toggleTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

