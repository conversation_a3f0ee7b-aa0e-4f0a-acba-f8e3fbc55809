import { type ButtonPosition, BUTTON_POSITION_STYLES } from '../types';

interface ChatbotButtonProps {
  position: ButtonPosition;
  onClick: () => void;
  isActive?: boolean;
}

export function ChatbotButton({ position, onClick, isActive = false }: ChatbotButtonProps) {
  const positionStyle = BUTTON_POSITION_STYLES[position];
  
  const positionStyles = Object.fromEntries(
    positionStyle.split(';').filter(Boolean).map(rule => {
      const [property, value] = rule.split(':').map(s => s.trim());
      return [property, value];
    })
  );

  return (
    <button
      className={`chatbot-trigger-button ${isActive ? 'active' : ''}`}
      onClick={onClick}
      style={positionStyles}
      title="Open Chatbot"
    >
      {isActive ? '×' : '💬'}
    </button>
  );
}

export default ChatbotButton;