import React from 'react';
import ReactMarkdown from 'react-markdown';
import { type Message } from '../types/message';
import { type SuggestedAction } from '../types/message';
import Avatar from './Avatar';
import MessageTimestamp from './MessageTimestamp';
import { SuggestedActions } from './SuggestedActions';
import Link from './Link';

interface MessageComponentProps {
  message: Message;
  onActionClick: (action: SuggestedAction) => void;
  isLatestBotMessage?: boolean;
}

const MessageComponent = React.memo(function MessageComponent({ message, onActionClick, isLatestBotMessage }: MessageComponentProps) {
  return (
    <div className="message-wrapper">
      <MessageTimestamp timestamp={message.timestamp} />
      <div className={`message ${message.isUser ? 'user' : 'bot'}`}>
        {!message.isUser && <Avatar isUser={message.isUser} />}
        <div className="message-content">
          {message.textFormat === 'markdown' ? (
            <ReactMarkdown
              components={{
                a: ({ href, children, ...props }) => (
                  <Link href={href || '#'} {...props}>
                    {children}
                  </Link>
                ),
              }}
            >
              {message.text}
            </ReactMarkdown>
          ) : (
            message.text
          )}
          {message.suggestedActions && isLatestBotMessage && (
            <SuggestedActions 
              suggestedActions={message.suggestedActions}
              onActionClick={onActionClick}
            />
          )}
        </div>
      </div>
    </div>
  );
});

export default MessageComponent;