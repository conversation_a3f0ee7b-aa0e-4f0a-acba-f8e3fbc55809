import React from 'react';
import { ChatbotProvider } from '../contexts/ChatBotProvider';
import { ThemeProvider } from '../contexts/ThemeContext';
import { useChatbot } from '../hooks/useChatbot';
import Chatbot from '../Chatbot';
import { type ChatArgs } from '../types';
import { getOverlayPositionStyle, getThemeFromNamespace } from '../utils';

interface ChatbotWidgetProps {
  initParams: ChatArgs;
  onClose?: () => void;
}

function ChatbotContent({ initParams, onClose }: ChatbotWidgetProps) {
  const { isInitialized, isConnected, initializeSession, initializeDirectLineConnection } = useChatbot();
  const [hasInitializedOnce, setHasInitializedOnce] = React.useState(false);

  // Expose both session and DirectLine initialization to global window for button click handler
  React.useEffect(() => {
    (window as any).initializeChatbotSession = () => {
      if (!isInitialized && !hasInitializedOnce) {
        console.log('🎯 Session initialization triggered');
        initializeSession(initParams);
        setHasInitializedOnce(true);
      } else {
        console.log('✅ Session already initialized, skipping');
      }
    };

    (window as any).initializeDirectLineConnection = () => {
      if (!isConnected) {
        console.log('🎯 DirectLine connection triggered');
        initializeDirectLineConnection();
      } else {
        console.log('✅ DirectLine already connected, skipping');
      }
    };
  }, [isInitialized, isConnected, hasInitializedOnce, initializeSession, initializeDirectLineConnection, initParams]);


  if (!isInitialized) {
    return null;
  }

  return (
    <div className="chatbot-overlay" style={getOverlayPositionStyle('right-bottom')}>
      <Chatbot onClose={onClose} />
    </div>
  );
}

export function ChatbotWidget({ initParams, onClose }: ChatbotWidgetProps) {
  // Convert namespace to theme name for compatibility
  
  const defaultThemeName = getThemeFromNamespace(initParams.namespace) as 'light' | 'dark' | 'blue' | 'green' | 'purple';
  
  // Always use proxy DirectLine configuration
  const proxyConfig = initParams.proxyConfig || {
    proxyUrl: 'http://localhost:3001',
    debug: true
  };
  
  return (
    <ThemeProvider defaultThemeName={defaultThemeName}>
      <ChatbotProvider proxyConfig={proxyConfig}>
        <ChatbotContent initParams={initParams} onClose={onClose} />
      </ChatbotProvider>
    </ThemeProvider>
  );
}