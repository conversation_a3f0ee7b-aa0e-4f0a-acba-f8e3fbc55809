import React from 'react';

interface AvatarProps {
  isUser?: boolean;
  className?: string;
}

const Avatar = React.memo(function Avatar({ isUser = false, className = '' }: AvatarProps) {
  return (
    <div className={`avatar ${isUser ? 'user-avatar' : 'bot-avatar'} ${className}`}>
      {isUser ? (
        <div className="user-avatar-placeholder">
          👤
        </div>
      ) : (
        <img 
          src="/eva.png" 
          alt="Eva - Assistant" 
          className="bot-avatar-image"
        />
      )}
    </div>
  );
});

export default Avatar;