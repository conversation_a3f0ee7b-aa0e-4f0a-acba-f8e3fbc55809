import React from 'react';
import { type SuggestedActions as SuggestedActionsType, type SuggestedAction } from '../types/message';

interface SuggestedActionsProps {
  suggestedActions: SuggestedActionsType;
  onActionClick: (action: SuggestedAction) => void;
}

export const SuggestedActions = React.memo(function SuggestedActions({ suggestedActions, onActionClick }: SuggestedActionsProps) {
  if (!suggestedActions?.actions?.length) {
    return null;
  }

  return (
    <div className="suggested-actions">
      {suggestedActions.actions.map((action, index) => (
        <button
          key={index}
          className="suggested-action-button"
          onClick={() => onActionClick(action)}
          type="button"
        >
          {action.title}
        </button>
      ))}
    </div>
  );
});