import React, { useState, useCallback } from 'react';
import { Send } from 'lucide-react';
import IconButton from './IconButton';

interface FormInputProps {
  onSubmit: (text: string, metadata?: Record<string, unknown>) => void | Promise<void>;
  isDisabled?: boolean;
  placeholder?: string;
}

export function FormInput({ 
  onSubmit, 
  isDisabled = false, 
  placeholder = "Type a message..." 
}: FormInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  const validateInput = useCallback((text: string): string | null => {
    const trimmed = text.trim();
    if (!trimmed) {
      return 'Message cannot be empty';
    }
    if (trimmed.length > 1000) {
      return 'Message is too long (max 1000 characters)';
    }
    return null;
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedValue = inputValue.trim();
    const error = validateInput(inputValue);
    
    if (error) {
      setValidationError(error);
      return;
    }

    setIsSubmitting(true);
    setValidationError(null);
    
    try {
      const result = onSubmit(trimmedValue);
      if (result instanceof Promise) {
        await result;
      }
      setInputValue('');
    } catch (error) {
      console.error('Form submission error:', error);
      setValidationError('Failed to send message. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [inputValue, onSubmit, validateInput]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as unknown as React.FormEvent);
    }
  }, [handleSubmit]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    if (validationError) {
      setValidationError(null);
    }
  }, [validationError]);

  const isFormDisabled = isDisabled || isSubmitting;
  const isSendDisabled = !inputValue.trim() || isFormDisabled;

  return (
    <form className="chatbot-input-container" onSubmit={handleSubmit}>
      <input
        type="text"
        className="chatbot-input"
        placeholder={placeholder}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        disabled={isFormDisabled}
        aria-label="Message input"
        aria-invalid={!!validationError}
        aria-describedby={validationError ? "input-error" : undefined}
        maxLength={1000}
      />
      <IconButton
        icon={Send}
        onClick={() => handleSubmit({ preventDefault: () => {} } as React.FormEvent)}
        className="primary"
        disabled={isSendDisabled}
        title={isSubmitting ? 'Sending...' : 'Send message'}
        size={20}
      />
      {validationError && (
        <div id="input-error" className="chatbot-input-error" role="alert">
          {validationError}
        </div>
      )}
    </form>
  );
}