import React from 'react';

interface LinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  target?: string;
  rel?: string;
}

const Link = React.memo(function Link({ 
  href, 
  children, 
  className = '', 
  target = '_blank',
  rel = 'noopener noreferrer'
}: LinkProps) {
  return (
    <a 
      href={href}
      className={`markdown-link ${className}`}
      target={target}
      rel={rel}
    >
      {children}
    </a>
  );
});

export default Link;