import React from 'react';
import { type LucideIcon } from 'lucide-react';

interface IconButtonProps {
  icon: LucideIcon;
  onClick?: () => void;
  className?: string;
  size?: number;
  disabled?: boolean;
  title?: string;
}

const IconButton = React.memo(function IconButton({ 
  icon: Icon, 
  onClick, 
  className = '', 
  size = 20, 
  disabled = false,
  title 
}: IconButtonProps) {
  return (
    <button 
      className={`icon-button ${className}`}
      onClick={onClick}
      disabled={disabled}
      title={title}
      type="button"
    >
      <Icon size={size} />
    </button>
  );
});

export default IconButton;