import { useContext } from 'react';
import { ThemeContext, type ThemeContextType } from '../contexts/themeContextDefinition';

export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Hook to get theme-aware CSS variables
export function useThemeTokens() {
  const { currentTheme } = useTheme();
  return currentTheme.tokens;
}