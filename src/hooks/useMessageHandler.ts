import React, { useCallback, useState, useRef, useEffect } from 'react';
import { io, Socket } from 'socket.io-client';
import { type Message, type ChatbotSession, type DirectLineActivity } from '../types';
import { type SessionManager } from '../services/sessionManager';

export interface ProxyDirectLineConfig {
  proxyUrl: string;
  debug?: boolean;
  apiKey?: string;
}

interface UseMessageHandlerProps {
  session: ChatbotSession | null;
  setIsTyping: (typing: boolean) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  setSession: (session: ChatbotSession) => void;
  sessionManager: SessionManager;
  proxyConfig: ProxyDirectLineConfig;
}

export function useMessageHandler({
  session,
  setIsTyping,
  setMessages,
  setSession,
  sessionManager,
  proxyConfig
}: UseMessageHandlerProps) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [conversationToken, setConversationToken] = useState<string | null>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const initializationRef = useRef(false);
  const socketRef = useRef<Socket | null>(null);

  // Stable event handlers
  const handleBotMessage = useCallback((data: { message: Message; activity: DirectLineActivity }) => {
    const { message, activity } = data;
    console.log('🤖 Bot message received via WebSocket:', data);
    console.log('🔍 Message suggestedActions:', message.suggestedActions);
    console.log('🔍 Activity suggestedActions:', activity.suggestedActions);
    
    // Ensure timestamp is a Date object
    let timestamp: Date;
    try {
      if (message.timestamp instanceof Date) {
        timestamp = message.timestamp;
      } else if (typeof message.timestamp === 'string' || typeof message.timestamp === 'number') {
        timestamp = new Date(message.timestamp);
      } else {
        timestamp = new Date();
      }
      
      // Validate the Date object
      if (isNaN(timestamp.getTime())) {
        timestamp = new Date();
      }
    } catch {
      console.warn('Invalid timestamp, using current time:', message.timestamp);
      timestamp = new Date();
    }
    
    const normalizedMessage: Message = {
      ...message,
      timestamp,
      // Explicitly preserve suggestedActions if present
      ...(message.suggestedActions && { suggestedActions: message.suggestedActions })
    };
    
    setIsTyping(false);
    
    setMessages((prev: Message[]) => {
      const messageExists = prev.some((msg: Message) => msg.id === normalizedMessage.id);
      if (messageExists) {
        console.log('🚫 Message already exists, skipping:', normalizedMessage.id);
        return prev;
      }
      console.log('✅ Adding new message to state:', normalizedMessage.text);
      console.log('📋 Message suggestedActions:', normalizedMessage.suggestedActions);
      return [...prev, normalizedMessage];
    });

    // Update session
    if (session) {
      const updatedSession = {
        ...session,
        messages: [...session.messages, normalizedMessage]
      };
      sessionManager.updateSession(updatedSession);
      setSession(updatedSession);
    }
  }, [session, sessionManager, setSession, setMessages, setIsTyping]);

  // Fetch message history for existing conversations
  const fetchMessageHistory = useCallback(async (): Promise<void> => {
    // Use session data for existing sessions, fallback to state for new sessions
    const targetConversationId = session?.conversationId || conversationId;
    const targetToken = session?.conversationToken || session?.accessToken || conversationToken;
    
    if (!targetConversationId || !targetToken) {
      console.warn('⚠️ Cannot fetch message history: missing conversation ID or token');
      console.log('Session data:', { 
        sessionConversationId: session?.conversationId,
        sessionToken: session?.conversationToken || session?.accessToken,
        stateConversationId: conversationId,
        stateToken: conversationToken
      });
      return;
    }

    try {
      console.log('📚 Fetching message history for existing conversation...', {
        conversationId: targetConversationId,
        hasToken: !!targetToken
      });
      
      const response = await fetch(`${proxyConfig.proxyUrl}/api/conversations/${targetConversationId}/messages?token=${encodeURIComponent(targetToken)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(proxyConfig.apiKey && { 'X-API-Key': proxyConfig.apiKey })
        }
      });

      if (!response.ok) {
        throw new Error(`Message history fetch failed: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Message history fetched:', data);

      // Process and display the historical messages if any
      if (data.messages && data.messages.length > 0) {
        console.log(`📜 Loading ${data.messages.length} historical messages`);
        
        // Add all historical messages in a single state update to prevent multiple re-renders
        setMessages((prev: Message[]) => {
          const newMessages = data.messages.filter((message: Message) => 
            !prev.some((msg: Message) => msg.id === message.id)
          );
          return [...prev, ...newMessages];
        });
      }

    } catch (error) {
      console.error('❌ Message history fetch error:', error);
    }
  }, [conversationId, conversationToken, session?.conversationId, session?.conversationToken, session?.accessToken, proxyConfig, setMessages]);

  // Send startConversation event to trigger welcome messages from CSP
  const sendStartConversationEvent = useCallback(async (): Promise<void> => {
    if (!conversationId || !conversationToken) {
      console.warn('⚠️ Cannot send startConversation event: DirectLine not connected');
      return;
    }

    try {
      console.log('🚀 Sending startConversation event to trigger welcome messages...');
      
      const startConversationActivity = {
        type: 'event',
        name: 'startConversation',
        from: { id: session?.userId || 'user' }
      };

      const response = await fetch(`${proxyConfig.proxyUrl}/api/conversations/${conversationId}/activities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(proxyConfig.apiKey && { 'X-API-Key': proxyConfig.apiKey })
        },
        body: JSON.stringify({
          token: conversationToken,
          activity: startConversationActivity
        })
      });

      if (!response.ok) {
        throw new Error(`startConversation event failed: ${response.status}`);
      }

      console.log('✅ startConversation event sent successfully');

    } catch (error) {
      console.error('❌ startConversation event error:', error);
    }
  }, [conversationId, conversationToken, session?.userId, proxyConfig]);

  const handleConnect = useCallback(() => {
    console.log('🌐 WebSocket connected to proxy server');
    setIsConnected(true);
    
    // Join conversation room if we have a conversation ID
    if (conversationId) {
      socket?.emit('join-conversation', { 
        conversationId, 
        userId: session?.userId || 'user' 
      });
      
      // Check if this is a new conversation or existing reconnection
      const isExistingSession = session?.isFromLocalStorage === true;
      
      if (isExistingSession) {
        console.log('🔄 Reconnecting to existing conversation, fetching message history...');
        // Fetch existing messages instead of sending startConversation
        setTimeout(() => {
          fetchMessageHistory();
        }, 1000);
      } else {
        console.log('🆕 New conversation, sending startConversation event...');
        // Send startConversation event to trigger welcome messages from CSP
        setTimeout(() => {
          sendStartConversationEvent();
        }, 1000);
      }
    }
  }, [conversationId, socket, session?.userId, session?.isFromLocalStorage, sendStartConversationEvent, fetchMessageHistory]);

  const handleDisconnect = useCallback(() => {
    console.log('🔌 WebSocket disconnected from proxy server');
    setIsConnected(false);
  }, []);

  const handleConnectError = useCallback((error: Error) => {
    console.error('❌ WebSocket connection error:', error);
    setIsConnected(false);
  }, []);

  // WebSocket event handlers - only re-run when socket changes
  useEffect(() => {
    if (!socket) return;

    socket.on('bot-message', handleBotMessage);
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connect_error', handleConnectError);

    return () => {
      socket.off('bot-message', handleBotMessage);
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connect_error', handleConnectError);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [socket]); // Only depend on socket to prevent WebSocket cleanup on handler changes

  // Get DirectLine token from proxy
  const getDirectLineToken = useCallback(async (): Promise<string> => {
    const response = await fetch(`${proxyConfig.proxyUrl}/api/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(proxyConfig.apiKey && { 'X-API-Key': proxyConfig.apiKey })
      },
      body: JSON.stringify({
        userId: session?.userId || `user_${Date.now()}`,
        sessionId: session?.id || `session_${Date.now()}`
      })
    });

    if (!response.ok) {
      throw new Error(`Token generation failed: ${response.status}`);
    }

    const data = await response.json();
    return data.token;
  }, [proxyConfig, session?.userId, session?.id]);

  // Create DirectLine conversation
  const createDirectLineConversation = useCallback(async (token: string): Promise<{ conversationId: string; conversationToken: string }> => {
    const response = await fetch(`${proxyConfig.proxyUrl}/api/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(proxyConfig.apiKey && { 'X-API-Key': proxyConfig.apiKey })
      },
      body: JSON.stringify({
        token,
        userId: session?.userId || `user_${Date.now()}`,
        sessionId: session?.id || `session_${Date.now()}`
      })
    });

    if (!response.ok) {
      throw new Error(`Conversation creation failed: ${response.status}`);
    }

    const data = await response.json();
    return {
      conversationId: data.conversationId,
      conversationToken: data.token
    };
  }, [proxyConfig, session?.userId, session?.id]);

  // Initialize DirectLine connection using WebSocket
  const initializeDirectLine = useCallback(async () => {
    if (initializationRef.current || isInitializing) {
      console.log('⚠️ DirectLine already initializing, skipping...');
      return;
    }

    initializationRef.current = true;
    setIsInitializing(true);
    
    try {
      console.log('🚀 Initializing DirectLine with WebSocket proxy...');
      
      let newConversationId: string;
      let newToken: string;
      
      // Check if we have existing session data to reconnect
      if (session?.conversationId && session?.conversationToken) {
        console.log('🔗 Attempting to reconnect to existing conversation:', session.conversationId);
        newConversationId = session.conversationId;
        newToken = session.conversationToken;
      } else {
        // Step 1: Get token from proxy
        const token = await getDirectLineToken();
        console.log('✅ DirectLine token received from proxy');
        
        // Step 2: Create new conversation
        const result = await createDirectLineConversation(token);
        newConversationId = result.conversationId;
        newToken = result.conversationToken;
        console.log('✅ DirectLine conversation created:', newConversationId);
      }
      
      setConversationId(newConversationId);
      setConversationToken(newToken);

      // Step 3: Initialize WebSocket connection
      const newSocket = io(proxyConfig.proxyUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        autoConnect: true
      });
      
      setSocket(newSocket);
      socketRef.current = newSocket;

      // Update session with conversation details
      if (session) {
        const updatedSession = {
          ...session,
          conversationId: newConversationId,
          conversationToken: newToken,
          accessToken: newToken,
          conversationUrl: `/conversations/${newConversationId}`
        };
        sessionManager.updateSession(updatedSession);
        setSession(updatedSession);
      }

      console.log('✅ WebSocket connection established, real-time streaming active');
      
    } catch (error) {
      console.error('❌ WebSocket DirectLine initialization failed:', error);
      setIsConnected(false);
      initializationRef.current = false;
    } finally {
      setIsInitializing(false);
    }
  }, [getDirectLineToken, createDirectLineConversation, session, sessionManager, setSession, proxyConfig, isInitializing]);

  const sendMessage = useCallback(async (text: string): Promise<void> => {
    if (!conversationId || !conversationToken) {
      throw new Error('DirectLine not connected');
    }

    try {
      console.log('📤 Sending message via proxy:', text);
      
      // Create user message
      const userMessage: Message = {
        id: `user-${Date.now()}`,
        text,
        isUser: true,
        timestamp: new Date()
      };

      // Add to UI immediately
      setMessages((prev: Message[]) => [...prev, userMessage]);

      // Update session
      if (session) {
        const updatedSession = {
          ...session,
          messages: [...session.messages, userMessage]
        };
        sessionManager.updateSession(updatedSession);
        setSession(updatedSession);
      }

      // Send via proxy REST API
      const response = await fetch(`${proxyConfig.proxyUrl}/api/conversations/${conversationId}/activities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(proxyConfig.apiKey && { 'X-API-Key': proxyConfig.apiKey })
        },
        body: JSON.stringify({
          token: conversationToken,
          activity: {
            type: 'message',
            text: text,
            from: { id: session?.userId || 'user' }
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Message send failed: ${response.status}`);
      }

      console.log('✅ Message sent successfully via proxy');

    } catch (error) {
      console.error('Message send error:', error);
      throw error;
    }
  }, [conversationId, conversationToken, session, sessionManager, setMessages, setSession, proxyConfig]);


  const clearMessages = useCallback(() => {
    setMessages([]);
    setIsTyping(false);
    if (session) {
      const updatedSession = { ...session, messages: [] };
      sessionManager.updateSession(updatedSession);
      setSession(updatedSession);
    }
  }, [session, sessionManager, setMessages, setIsTyping, setSession]);

  // Cleanup on unmount only
  useEffect(() => {
    return () => {
      if (socketRef.current) {
        console.log('🧹 Cleaning up WebSocket connection on unmount');
        socketRef.current.disconnect();
      }
    };
  }, []); // Empty dependency array - only runs on unmount

  return { 
    sendMessage, 
    clearMessages, 
    initializeDirectLine,
    sendStartConversationEvent,
    isConnected 
  };
}