import { type ChatbotSession, type ChatArgs, type VisibilitySettings, type SessionOptions } from '../types';

const SESSION_STORAGE_KEY = 'chatbot-session';
const SESSION_EXPIRY_HOURS = 24; // Session expires after 24 hours

export class SessionManager {
  private static instance: SessionManager;
  private currentSession: ChatbotSession | null = null;

  private constructor() {}

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  /**
   * Check if a valid session exists
   */
  public hasValidSession(): boolean {
    console.log('🔍 Checking for valid session in localStorage...');
    const session = this.loadSession();
    if (!session) {
      console.log('❌ No session found in localStorage');
      return false;
    }

    // Check if session has expired (only if it has createdAt)
    if (session.createdAt) {
      const now = new Date();
      const sessionAge = now.getTime() - new Date(session.createdAt).getTime();
      const maxAge = SESSION_EXPIRY_HOURS * 60 * 60 * 1000; // Convert hours to ms

      if (sessionAge > maxAge) {
        console.log('⏰ Session expired, clearing...');
        this.clearSession();
        return false;
      }
    }

    console.log('✅ Valid session found:', {
      conversationId: session.conversationId,
      hasAccessToken: !!session.accessToken,
      hasConversationUrl: !!session.conversationUrl
    });
    this.currentSession = session;
    return true;
  }

  /**
   * Create a new session with initialization parameters
   */
  public createSession(params: ChatArgs): ChatbotSession {
    const session: ChatbotSession = {
      id: this.generateSessionId(),
      authToken: params.authToken,
      context: params.context,
      topic: params.topic,
      messages: [],
      createdAt: new Date(),
      userId: `user_${Date.now()}`,
      isFromLocalStorage: false, // Mark as new session
    };

    this.currentSession = session;
    this.saveSession(session);
    return session;
  }

  /**
   * Get the current session
   */
  public getCurrentSession(): ChatbotSession | null {
    return this.currentSession;
  }

  /**
   * Update the current session
   */
  public updateSession(session: ChatbotSession): void {
    this.currentSession = session;
    this.saveSession(session);
  }

  /**
   * Clear the current session
   */
  public clearSession(): void {
    this.currentSession = null;
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(SESSION_STORAGE_KEY);
    }
  }

  /**
   * Update visibility settings in the current session
   */
  public updateVisibility(visibility: VisibilitySettings): void {
    if (this.currentSession) {
      this.currentSession.visibility = { ...this.currentSession.visibility, ...visibility };
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Update session options
   */
  public updateOptions(options: Record<string, unknown>): void {
    if (this.currentSession) {
      this.currentSession.options = { ...this.currentSession.options, ...options };
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Set access token for Bot Framework integration
   */
  public setAccessToken(accessToken: string): void {
    if (this.currentSession) {
      this.currentSession.accessToken = accessToken;
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Set conversation URL for Bot Framework integration
   */
  public setConversationUrl(conversationUrl: string): void {
    if (this.currentSession) {
      this.currentSession.conversationUrl = conversationUrl;
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Set conversation ID for DirectLine integration
   */
  public setConversationId(conversationId: string): void {
    if (this.currentSession) {
      this.currentSession.conversationId = conversationId;
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Set conversation token for DirectLine integration
   */
  public setConversationToken(conversationToken: string): void {
    if (this.currentSession) {
      this.currentSession.conversationToken = conversationToken;
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Get the user ID for DirectLine communication
   */
  public getUserId(): string | null {
    return this.currentSession?.userId || null;
  }

  /**
   * Store custom data in the session
   */
  public setCustomData(key: string, value: unknown): void {
    if (this.currentSession) {
      if (!this.currentSession.customData) {
        this.currentSession.customData = {};
      }
      this.currentSession.customData[key] = value;
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Get custom data from the session
   */
  public getCustomData(key: string): unknown {
    return this.currentSession?.customData?.[key];
  }

  /**
   * Store the complete session data structure
   */
  public setSessionData(sessionData: {
    visibility?: VisibilitySettings;
    options?: SessionOptions;
    accessToken?: string;
    conversationUrl?: string;
    customData?: Record<string, unknown>;
  }): void {
    if (this.currentSession) {
      if (sessionData.visibility) {
        this.currentSession.visibility = sessionData.visibility;
      }
      if (sessionData.options) {
        this.currentSession.options = sessionData.options;
      }
      if (sessionData.accessToken) {
        this.currentSession.accessToken = sessionData.accessToken;
      }
      if (sessionData.conversationUrl) {
        this.currentSession.conversationUrl = sessionData.conversationUrl;
      }
      if (sessionData.customData) {
        this.currentSession.customData = { ...this.currentSession.customData, ...sessionData.customData };
      }
      this.saveSession(this.currentSession);
    }
  }

  /**
   * Get the complete session data
   */
  public getSessionData(): {
    visibility?: VisibilitySettings;
    options?: SessionOptions;
    accessToken?: string;
    conversationUrl?: string;
    customData?: Record<string, unknown>;
  } {
    if (!this.currentSession) return {};
    
    return {
      visibility: this.currentSession.visibility,
      options: this.currentSession.options || {},
      accessToken: this.currentSession.accessToken,
      conversationUrl: this.currentSession.conversationUrl,
      customData: this.currentSession.customData
    };
  }

  /**
   * Load session from sessionStorage with new format
   */
  private loadSession(): ChatbotSession | null {
    if (typeof localStorage === 'undefined') return null;

    try {
      const sessionData = localStorage.getItem(SESSION_STORAGE_KEY);
      console.log('💾 Raw session data from localStorage:', sessionData);
      
      if (!sessionData) {
        console.log('❌ No session data in localStorage');
        return null;
      }

      const parsed = JSON.parse(sessionData);
      console.log('🔍 Parsed session data:', parsed);
      
      // Check if it's the new format: {visibility, options, accessToken, conversationUrl}
      if (parsed.accessToken && parsed.conversationUrl) {
        console.log('📱 Found existing session data, attempting to reconnect...');
        
        const conversationId = this.extractConversationId(parsed.conversationUrl);
        console.log('🆔 Extracted conversation ID:', conversationId);
        
        // Convert to ChatbotSession format
        const session: ChatbotSession = {
          id: this.generateSessionId(),
          userId: `user_${Date.now()}`, // Keep generating new user ID for each session
          messages: [], // Start with empty messages - they'll be loaded from DirectLine
          createdAt: new Date(),
          visibility: parsed.visibility,
          options: parsed.options || {},
          accessToken: parsed.accessToken,
          conversationUrl: parsed.conversationUrl,
          conversationId: conversationId,
          conversationToken: parsed.accessToken, // Use accessToken as conversation token
          isFromLocalStorage: true // Mark as loaded from localStorage
        };
        
        console.log('✅ Session converted for reconnection:', {
          conversationId: session.conversationId,
          conversationUrl: session.conversationUrl,
          hasToken: !!session.conversationToken
        });
        
        return session;
      }
      
      console.log('⚠️ Session data missing required fields (accessToken/conversationUrl), clearing...');
      // If it's an old format or invalid, clear it
      localStorage.removeItem(SESSION_STORAGE_KEY);
      return null;
      
    } catch (error) {
      console.error('Failed to load chatbot session:', error);
      localStorage.removeItem(SESSION_STORAGE_KEY);
      return null;
    }
  }

  /**
   * Save session to sessionStorage in the new format
   */
  private saveSession(session: ChatbotSession): void {
    if (typeof sessionStorage === 'undefined') return;

    try {
      // Save in the simplified format: {visibility, options, accessToken, conversationUrl}
      const sessionData = {
        visibility: session.visibility || { window: "open" },
        options: session.options || {},
        accessToken: session.accessToken || session.conversationToken,
        conversationUrl: session.conversationUrl
      };
      
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessionData));
      console.log('💾 Session saved in new format:', sessionData);
    } catch (error) {
      console.error('Failed to save chatbot session:', error);
    }
  }

  /**
   * Extract conversation ID from DirectLine conversation URL
   */
  private extractConversationId(conversationUrl: string): string | undefined {
    try {
      // Extract from URL pattern like: /channels/api/v2/client/.../conversations/CONVERSATION_ID
      const match = conversationUrl.match(/\/conversations\/([^/]+)$/);
      return match ? match[1] : undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `chatbot-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

}