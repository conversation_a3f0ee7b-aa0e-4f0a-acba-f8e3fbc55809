import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ChatbotWidget } from './components';
import { type ChatArgs } from './types';
import { getThemeByNamespace } from './theme';
import chatbotAPI from './api/chatbot-api';
import { generateThemeCSS } from './theme';
import { SessionManager } from './services/sessionManager';
import { createFloatingButton } from './utils';


// Global initialization parameters
let globalInitParams: ChatArgs = {
  namespace: 'light-namespace',
  layoutMode: 'window',
  visibility: 'minimized'
};

let isWidgetInitialized = false;
let widgetRoot: ReturnType<typeof createRoot> | null = null;
let buttonElement: HTMLElement | null = null;
let isWidgetVisible = false;

// Placeholder for removed function - using util import instead

// Function to create the floating button outside Shadow DOM
function createButtonElement(params: ChatArgs, colors: { [key: string]: string }) {
  buttonElement = createFloatingButton(params, colors, buttonElement, () => {
    openChatbot();
  });
}

// Function to open the chatbot
function openChatbot() {
  if (buttonElement) {
    buttonElement.style.display = 'none';
  }

  isWidgetVisible = true;
  renderWidget();
  
  // Check if session/DirectLine are already initialized (from auto-init)
  setTimeout(() => {
    // Initialize session if not already done
    if ((window as any).initializeChatbotSession) {
      (window as any).initializeChatbotSession();
    }
    
    // Initialize DirectLine connection if not already done  
    setTimeout(() => {
      if ((window as any).initializeDirectLineConnection) {
        (window as any).initializeDirectLineConnection();
      }
    }, 200);
  }, 100);
}

// Function to close the chatbot
function closeChatbot() {
  if (buttonElement) {
    buttonElement.style.display = 'flex';
  }

  isWidgetVisible = false;
  renderWidget();
}

// Function to check for existing session and auto-initialize
function checkAndAutoInitializeExistingSession() {
  // Import SessionManager to check for existing session
  setTimeout(() => {
    try {
      // Check localStorage directly for existing session
      const sessionData = localStorage.getItem('chatbot-session');
      if (sessionData) {
        const parsed = JSON.parse(sessionData);
        if (parsed.accessToken && parsed.conversationUrl) {
          console.log('🔄 Existing session found, auto-initializing...');
          
          // Update button to show session indicator
          if (buttonElement) {
            buttonElement.innerHTML = '💬<span style="position: absolute; top: 0; right: 0; width: 8px; height: 8px; background: #00ff00; border-radius: 50%; border: 1px solid white;"></span>';
            buttonElement.title = 'Continue Conversation';
          }
          
          // Render widget in hidden state and initialize session + DirectLine
          isWidgetVisible = false; // Keep hidden initially
          renderWidget();
          
          // Auto-initialize session and DirectLine connection
          setTimeout(() => {
            if ((window as any).initializeChatbotSession) {
              (window as any).initializeChatbotSession();
            }
            setTimeout(() => {
              if ((window as any).initializeDirectLineConnection) {
                (window as any).initializeDirectLineConnection();
              }
            }, 200);
          }, 300);
        }
      }
    } catch (error) {
      console.error('Error checking for existing session:', error);
    }
  }, 100);
}

// Function to render the widget based on visibility
function renderWidget() {
  const targetElement = document.getElementById('chatbot-widget');
  if (targetElement && widgetRoot) {
    if (isWidgetVisible) {
      // Show the widget and ensure the container is visible
      const shadowRoot = targetElement.shadowRoot;
      if (shadowRoot) {
        const reactRoot = shadowRoot.getElementById('react-root');
        if (reactRoot) {
          reactRoot.style.display = 'block';
        }
      }
      
      widgetRoot.render(
        <StrictMode>
          <ChatbotWidget initParams={globalInitParams} onClose={closeChatbot} />
        </StrictMode>
      );
    } else {
      // Hide widget but keep it mounted to preserve WebSocket connection
      const shadowRoot = targetElement.shadowRoot;
      if (shadowRoot) {
        const reactRoot = shadowRoot.getElementById('react-root');
        if (reactRoot) {
          reactRoot.style.display = 'none';
        }
      }
    }
  }
}

// Function to initialize the chatbot widget
function initializeChatbot(params: ChatArgs = {}) {
  // Merge with global params
  globalInitParams = { ...globalInitParams, ...params };
  
  // Get theme based on namespace
  const namespace = params.namespace || 'light-namespace';
  const theme = getThemeByNamespace(namespace);
  const colors = theme.tokens.colors;
  
  if (isWidgetInitialized && widgetRoot) {
    // Widget already initialized - update theme and recreate button
    const targetElement = document.getElementById('chatbot-widget');
    if (targetElement && targetElement.shadowRoot) {
      const existingStyle = targetElement.shadowRoot.querySelector('style');
      if (existingStyle) {
        existingStyle.textContent = generateThemeCSS(colors);
      }
    }
    
    // Recreate button with new theme
    createButtonElement(globalInitParams, colors);
    return;
  }
  
  // Find or create the target element
  let targetElement = document.getElementById('chatbot-widget');
  
  if (!targetElement) {
    // Create the div automatically
    targetElement = document.createElement('div');
    targetElement.id = 'chatbot-widget';
    document.body.appendChild(targetElement);
  }

  // Check if shadow root already exists
  let shadowRoot = targetElement.shadowRoot;
  if (!shadowRoot) {
    // Create shadow DOM
    shadowRoot = targetElement.attachShadow({ mode: 'open' });
    
    // Create style element and inject CSS
    const styleElement = document.createElement('style');
    styleElement.textContent = generateThemeCSS(colors);
    
    shadowRoot.appendChild(styleElement);
    
    // Create container for React app
    const reactContainer = document.createElement('div');
    reactContainer.id = 'react-root';
    shadowRoot.appendChild(reactContainer);
  }

  // Get or create React root
  const reactContainer = shadowRoot.getElementById('react-root')!;
  if (!widgetRoot) {
    widgetRoot = createRoot(reactContainer);
  }
  
  // Create the floating button outside shadow DOM
  createButtonElement(globalInitParams, colors);
  
  // Setup API integration
  setupAPIIntegration();
  
  // Check if existing session exists and auto-initialize if so
  checkAndAutoInitializeExistingSession();
  
  isWidgetInitialized = true;
}

// Setup API integration with window functions
function setupAPIIntegration() {
  // Connect chatbot API to our internal functions
  window.openChatbot = openChatbot;
  window.closeChatbot = closeChatbot;
  
  // These will be set by the widget context when it's ready
  (window as any).initializeChatbotSession = undefined;
  (window as any).initializeDirectLineConnection = undefined;
  window.setChatbotUserInfo = (userInfo: Record<string, unknown>) => {
    if (globalInitParams.context) {
      globalInitParams.context.variables = { 
        ...globalInitParams.context.variables,
        name: userInfo.name as string
      };
    } else {
      globalInitParams.context = {
        variables: { name: userInfo.name as string }
      };
    }
  };
  window.setChatbotTopic = (topic: string, message?: string) => {
    globalInitParams.topic = topic;
    // TODO: Use message parameter for initial message when implementing topic switching
    if (message) {
      console.log('Topic message:', message);
    }
  };
  window.setChatbotPosition = (position: string) => {
    // Position is now handled via layout mode - for backward compatibility
    console.log('Position setting:', position);
  };
  window.clearChatbotMessages = () => {
    // This will be implemented when we add message clearing
    console.log('Clear messages requested');
  };
  window.destroyChatbot = () => {
    if (buttonElement && buttonElement.parentNode) {
      buttonElement.parentNode.removeChild(buttonElement);
    }
    if (widgetRoot) {
      widgetRoot.render(<></>);
    }
    isWidgetInitialized = false;
  };

  // Session data management functions
  window.setChatbotSessionData = (sessionData: any) => {
    const sessionManager = SessionManager.getInstance();
    sessionManager.setSessionData(sessionData);
    console.log('Session data stored:', sessionData);
  };

  window.getChatbotSessionData = () => {
    const sessionManager = SessionManager.getInstance();
    return sessionManager.getSessionData();
  };

  window.clearChatbotSession = () => {
    const sessionManager = SessionManager.getInstance();
    sessionManager.clearSession();
    console.log('Session cleared');
  };
}

// Auto-initialize with default params when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => initializeChatbot());
} else {
  initializeChatbot();
}

// Connect to the API system
chatbotAPI.initializeChatbot = initializeChatbot;


// Auto-initialize the API system
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    // Use the window API to trigger initialization
    if (window.ChatbotWidget) {
      window.ChatbotWidget.push({ action: 'init', args: {} });
    }
  });
} else {
  // Use the window API to trigger initialization
  if (window.ChatbotWidget) {
    window.ChatbotWidget.push({ action: 'init', args: {} });
  }
}

export { initializeChatbot };