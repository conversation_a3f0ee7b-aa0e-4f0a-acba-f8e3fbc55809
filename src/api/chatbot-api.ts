import { 
  type ChatbotInstruction, 
  type Cha<PERSON><PERSON>State, 
  type Chatbot<PERSON><PERSON>backTypeF<PERSON>,
  type Chatbot<PERSON>ventMap,
  type ChatbotError,
  type ChatbotMessage,
  type ChatArgs
} from '../types';

// Initialize browser context
if (typeof window !== 'undefined') {
  window.ChatbotWidget = window.ChatbotWidget ?? { push: () => {} };
}

class ChatbotAPI {
  private instructionQueue: ChatbotInstruction[] = [];
  private isInitialized: boolean = false;
  private eventListeners: Map<string, Set<(...args: unknown[]) => void>> = new Map();
  private currentState: ChatbotState = {
    isOpen: false,
    isInitialized: false,
    hasMessages: false,
    messageCount: 0,
    currentTheme: 'light',
    position: 'right-bottom'
  };

  constructor() {
    this.setupWindowAPI();
  }

  private setupWindowAPI(): void {
    if (typeof window === 'undefined') return;

    window.ChatbotWidget = {
      push: (instruction: ChatbotInstruction) => this.processInstruction(instruction),
      initialize: (params?: ChatArgs) => this.legacyInitialize(params)
    };
  }

  private processInstruction(instruction: ChatbotInstruction): void {
    // Always allow init instructions
    if (instruction.action === 'init') {
      this.handleInit(instruction);
      this.processQueuedInstructions();
      return;
    }

    // Queue instructions if not initialized
    if (!this.isInitialized) {
      this.instructionQueue.push(instruction);
      return;
    }

    // Process instruction immediately if initialized
    this.handleInstruction(instruction);
  }

  private handleInit(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'init') return;

    const params = ('args' in instruction ? instruction.args : {}) as ChatArgs;
    
    // Call the main initialization function if available
    if (this.initializeChatbot) {
      this.initializeChatbot(params);
    }
    
    this.isInitialized = true;

    this.updateState({
      isInitialized: true,
    });

    this.emit('widget.initialized', this.currentState);
  }

  private processQueuedInstructions(): void {
    while (this.instructionQueue.length > 0) {
      const instruction = this.instructionQueue.shift();
      if (instruction) {
        this.handleInstruction(instruction);
      }
    }
  }

  private handleInstruction(instruction: ChatbotInstruction): void {
    try {
      switch (instruction.action) {
        case 'open':
          this.handleOpen();
          break;
        case 'close':
          this.handleClose();
          break;
        case 'sendMessage':
          this.handleSendMessage(instruction);
          break;
        case 'setUserInfo':
          this.handleSetUserInfo(instruction);
          break;
        case 'setTopic':
          this.handleSetTopic(instruction);
          break;
        case 'setVariable':
          this.handleSetVariable(instruction);
          break;
        case 'on':
          this.handleOn(instruction);
          break;
        case 'off':
          this.handleOff(instruction);
          break;
        case 'clearMessages':
          this.handleClearMessages();
          break;
        case 'destroy':
          this.handleDestroy();
          break;
        case 'getState':
          this.handleGetState(instruction);
          break;
        case 'setSessionData':
          this.handleSetSessionData(instruction);
          break;
        case 'getSessionData':
          this.handleGetSessionData(instruction);
          break;
        case 'clearSession':
          this.handleClearSession();
          break;
        case 'initializeConnection':
          this.handleInitializeConnection();
          break;
        case 'initializeSession':
          this.handleInitializeSession();
          break;
        default:
          this.emitError('UNKNOWN_ACTION', `Unknown action: ${instruction.action}`);
      }
    } catch (error) {
      this.emitError('INSTRUCTION_ERROR', `Error processing instruction: ${instruction.action}`, error);
    }
  }

  // Instruction handlers
  private handleOpen(): void {
    if (typeof window.openChatbot === 'function') {
      window.openChatbot();
      this.updateState({ isOpen: true });
      this.emit('widget.opened', this.currentState);
    }
  }

  private handleClose(): void {
    if (typeof window.closeChatbot === 'function') {
      window.closeChatbot();
      this.updateState({ isOpen: false });
      this.emit('widget.closed', this.currentState);
    }
  }

  private handleSendMessage(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'sendMessage') return;
    const { text, metadata } = instruction.args;
    if (typeof window.sendChatbotMessage === 'function') {
      const message: ChatbotMessage = {
        id: Date.now().toString(),
        text,
        isUser: true,
        timestamp: new Date(),
        metadata
      };
      window.sendChatbotMessage(text);
      this.emit('message.sent', message);
      this.updateState({ 
        hasMessages: true,
        messageCount: this.currentState.messageCount + 1
      });
    }
  }


  private handleSetUserInfo(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'setUserInfo') return;
    const { userInfo } = instruction.args;
    if (typeof window.setChatbotUserInfo === 'function') {
      window.setChatbotUserInfo(userInfo);
    }
  }

  private handleSetTopic(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'setTopic') return;
    const { topic, message } = instruction.args;
    if (typeof window.setChatbotTopic === 'function') {
      window.setChatbotTopic(topic, message);
    }
  }

  private handleSetVariable(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'setVariable') return;
    const { key, value } = instruction.args;
    
    // Handle special cases for backwards compatibility
    if (key === 'theme' && typeof window.setChatbotTheme === 'function' && typeof value === 'string') {
      window.setChatbotTheme(value);
      this.updateState({ currentTheme: value });
      this.emit('theme.changed', value);
    } else if (key === 'position' && typeof window.setChatbotPosition === 'function' && typeof value === 'string') {
      window.setChatbotPosition(value);
      this.updateState({ position: value as 'left-bottom' | 'right-bottom' | 'right-top' | 'left-top' });
    } else {
      // Generic variable setting - emit event for custom handling
      this.emit('variable.set', { key, value });
    }
  }

  private handleOn(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'on') return;
    const { event, callback } = instruction.args;
    this.on(event, callback);
  }

  private handleOff(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'off') return;
    const { event } = instruction.args;
    this.off(event);
  }

  private handleClearMessages(): void {
    if (typeof window.clearChatbotMessages === 'function') {
      window.clearChatbotMessages();
      this.updateState({ 
        hasMessages: false,
        messageCount: 0
      });
    }
  }

  private handleDestroy(): void {
    this.eventListeners.clear();
    this.isInitialized = false;
    this.updateState({
      isOpen: false,
      isInitialized: false,
      hasMessages: false,
      messageCount: 0
    });
    if (typeof window.destroyChatbot === 'function') {
      window.destroyChatbot();
    }
  }

  private handleGetState(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'getState') return;
    if (instruction.callback && typeof instruction.callback === 'function') {
      instruction.callback(this.currentState);
    }
  }

  private handleSetSessionData(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'setSessionData') return;
    const sessionData = instruction.args;
    
    if (typeof window.setChatbotSessionData === 'function') {
      window.setChatbotSessionData(sessionData);
    }
  }

  private handleGetSessionData(instruction: ChatbotInstruction): void {
    if (instruction.action !== 'getSessionData') return;
    
    if (instruction.callback && typeof instruction.callback === 'function') {
      if (typeof window.getChatbotSessionData === 'function') {
        const sessionData = window.getChatbotSessionData();
        instruction.callback(sessionData);
      } else {
        instruction.callback({});
      }
    }
  }

  private handleClearSession(): void {
    if (typeof window.clearChatbotSession === 'function') {
      window.clearChatbotSession();
    }
  }

  private handleInitializeConnection(): void {
    if (typeof window.initializeDirectLineConnection === 'function') {
      window.initializeDirectLineConnection();
    }
  }

  private handleInitializeSession(): void {
    if (typeof (window as any).initializeChatbotSession === 'function') {
      (window as any).initializeChatbotSession();
    }
  }

  // Event management
  private on<E extends keyof ChatbotEventMap>(event: E, callback: ChatbotCallbackTypeFor<E>): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback as (...args: unknown[]) => void);
  }

  private off(event: string): void {
    this.eventListeners.delete(event);
  }

  private emit<E extends keyof ChatbotEventMap>(event: E, data?: Parameters<ChatbotEventMap[E]>[0]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  private emitError(code: string, message: string, details?: unknown): void {
    const error: ChatbotError = { code, message, details };
    this.emit('error', error);
    console.error(`Chatbot API Error [${code}]: ${message}`, details);
  }

  // State management
  private updateState(updates: Partial<ChatbotState>): void {
    this.currentState = { ...this.currentState, ...updates };
  }

  // Legacy compatibility
  private legacyInitialize(params?: ChatArgs): void {
    this.processInstruction({ action: 'init', args: params });
  }

  // Internal method for main.tsx to set
  public initializeChatbot: ((params: ChatArgs) => void) | null = null;

  // Public methods for internal use
  public notifyMessageReceived(message: ChatbotMessage): void {
    this.emit('message.received', message);
    this.updateState({ 
      hasMessages: true,
      messageCount: this.currentState.messageCount + 1
    });
  }

  public notifyTypingStart(): void {
    this.emit('typing.start');
  }

  public notifyTypingStop(): void {
    this.emit('typing.stop');
  }

  public notifySessionCreated(sessionId: string, userInfo?: Record<string, unknown>): void {
    this.updateState({ sessionId });
    this.emit('session.created', { sessionId, userInfo });
  }

  public getState(): ChatbotState {
    return { ...this.currentState };
  }
}

// Create singleton instance
const chatbotAPI = new ChatbotAPI();

// Extend window with internal methods for main.tsx
declare global {
  interface Window {
    chatbotAPI: ChatbotAPI;
    sendChatbotMessage?: (text: string) => void;
    setChatbotTheme?: (theme: string) => void;
  }
}

if (typeof window !== 'undefined') {
  window.chatbotAPI = chatbotAPI;
}

export default chatbotAPI;