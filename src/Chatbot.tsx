import { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useChatbot } from './hooks/useChatbot';
import { Avatar, MessageComponent, FormInput, IconButton } from './components';
import { type SuggestedAction } from './types/message';
import { X, Maximize, Minimize } from 'lucide-react';
import { findLatestBotMessageIndex, scrollToBottom } from './utils';

interface AppProps {
  onClose?: () => void;
}

function Chatbot({ onClose }: AppProps) {
  const { messages, isTyping, sendMessage } = useChatbot();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isFullPage, setIsFullPage] = useState(false);

  const handleActionClick = useCallback((action: SuggestedAction) => {
    sendMessage(action.text || action.value);
  }, [sendMessage]);

  // Find the latest bot message index
  const latestBotMessageIndex = useMemo(() => 
    findLatestBotMessageIndex(messages), [messages]);

  const handleScrollToBottom = () => {
    scrollToBottom(messagesEndRef.current);
  };

  useEffect(() => {
    handleScrollToBottom();
  }, [messages]);

  // Scroll to bottom when switching between modes to maintain UX
  useEffect(() => {
    const timer = setTimeout(() => {
      handleScrollToBottom();
    }, 200); // Wait for animation to settle

    return () => clearTimeout(timer);
  }, [isFullPage]);

  return (
    <div className={`chatbot-container ${isFullPage ? 'fullpage' : 'sidebar'}`}>
      <div className="chatbot-header">
        <span>Eva</span>
        <div style={{ display: 'flex', gap: '4px' }}>
          <IconButton
            icon={isFullPage ? Minimize : Maximize}
            onClick={() => setIsFullPage(!isFullPage)}
            title={isFullPage ? 'Switch to side mode' : 'Switch to full page'}
            size={20}
          />
          {onClose && (
            <IconButton
              icon={X}
              onClick={onClose}
              title="Close chat"
              size={20}
            />
          )}
        </div>
      </div>

      <div className="chatbot-messages">
        {messages.map((message, index) => (
          <MessageComponent
            key={message.id}
            message={message}
            onActionClick={handleActionClick}
            isLatestBotMessage={!message.isUser && index === latestBotMessageIndex}
          />
        ))}

        {isTyping && (
          <div className="message-wrapper">
            <div className="message bot">
              <Avatar isUser={false} />
              <div className="message-content">
                <em>Typing...</em>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <FormInput 
        onSubmit={sendMessage}
        isDisabled={isTyping}
        placeholder="Type a message..."
      />
    </div>
  );
}

export default Chatbot;