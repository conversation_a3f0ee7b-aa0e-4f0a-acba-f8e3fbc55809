
/**
 * Generates complete CSS string for the chatbot widget
 * Optimized for Shadow DOM injection with theme token interpolation
 */
export function generateThemeCSS(colors: { [key: string]: string }): string {
  return `
    /* Etelka Font Family */
    @font-face {
      font-family: 'Etelka';
      src: url('/fonts/etelkaLight.woff2') format('woff2'),
           url('/fonts/etelkaLight.ttf') format('truetype');
      font-weight: 300;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Etelka';
      src: url('/fonts/etelkaMedium.woff2') format('woff2'),
           url('/fonts/etelkaMedium.ttf') format('truetype');
      font-weight: 500;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Etelka';
      src: url('/fonts/etelkaBold.woff2') format('woff2'),
           url('/fonts/etelkaBold.ttf') format('truetype');
      font-weight: 700;
      font-style: normal;
      font-display: swap;
    }
    
    @font-face {
      font-family: 'Etelka';
      src: url('/fonts/etelkaBlack.woff2') format('woff2'),
           url('/fonts/etelkaBlack.ttf') format('truetype');
      font-weight: 900;
      font-style: normal;
      font-display: swap;
    }
    .chatbot-container {
      position: fixed;
      bottom: 24px;
      right: 24px;
      width: 350px;
      height: 500px;
      background: ${colors.background};
      border: 1px solid ${colors.border};
      border-radius: 12px;
      box-shadow: 0 4px 20px ${colors.shadow};
      display: flex;
      flex-direction: column;
      font-family: "Etelka", -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      z-index: 10000;
      transform-origin: bottom right;
      transition: width 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  height 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  left 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  top 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  transform 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  border-radius 0.4s cubic-bezier(0.2, 0.8, 0.2, 1),
                  box-shadow 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    }

    .chatbot-container.sidebar {
      width: 350px;
      height: 500px;
      bottom: 24px;
      right: 24px;
      left: auto;
      top: auto;
      transform: scale(1);
      border-radius: 12px;
      box-shadow: 0 4px 20px ${colors.shadow};
    }

    .chatbot-container.fullpage {
      width: calc(100vw - 40px);
      height: calc(100vh - 40px);
      bottom: 20px;
      right: 20px;
      left: 20px;
      top: 20px;
      transform: scale(1);
      border-radius: 8px;
      box-shadow: 0 8px 32px ${colors.shadow};
    }

    .chatbot-overlay {
      position: fixed;
      bottom: 90px;
      right: 24px;
      z-index: 10000;
    }

    .chatbot-header {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
      padding: 16px;
      border-radius: 12px 12px 0 0;
      font-weight: 700;
      font-size: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      transition: border-radius 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
    }

    .chatbot-container.fullpage .chatbot-header {
      border-radius: 8px 8px 0 0;
    }

    .chatbot-close-button {
      background: none;
      border: none;
      color: ${colors.textOnPrimary};
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: 0.15s ease;
    }

    .chatbot-close-button:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .chatbot-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 12px;
      background: ${colors.background};
    }

    .message-wrapper {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .message-timestamp {
      font-size: 11px;
      color: ${colors.textSecondary};
      text-align: center;
      margin: 0 auto;
      padding: 2px 8px;
    }

    .message {
      max-width: 80%;
      display: flex;
      align-items: flex-start;
      gap: 8px;
      font-size: 14px;
      line-height: 1.4;
    }

    .message.user {
      align-self: flex-end;
      flex-direction: row-reverse;
    }

    .message.bot {
      align-self: flex-start;
    }

    .message-content {
      padding: 8px 16px;
      border-radius: 18px;
      max-width: 100%;
    }

    .message.user .message-content {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
    }

    .message.bot .message-content {
      background: ${colors.surface};
      color: ${colors.text};
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    .bot-avatar {
      background: ${colors.surface};
      border: 2px solid ${colors.border};
    }

    .bot-avatar-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    .user-avatar {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
      font-size: 16px;
    }

    .user-avatar-placeholder {
      font-size: 16px;
    }

    .chatbot-input-container {
      display: flex;
      padding: 16px;
      border-top: 1px solid ${colors.border};
      gap: 8px;
      background: ${colors.background};
    }

    .chatbot-input {
      flex: 1;
      padding: 8px 16px;
      border: 1px solid ${colors.border};
      border-radius: 20px;
      outline: none;
      font-size: 14px;
      background: ${colors.background};
      color: ${colors.text};
      font-family: "Etelka", -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .chatbot-input:focus {
      border-color: ${colors.primary};
    }

    .chatbot-input::placeholder {
      color: ${colors.textSecondary};
    }

    .chatbot-send-button {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
      border: none;
      border-radius: 20px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: 0.15s ease;
      font-family: "Etelka", -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .chatbot-send-button:hover {
      background: ${colors.primaryHover};
    }

    .chatbot-send-button:active {
      background: ${colors.primaryActive};
    }

    .chatbot-send-button:disabled {
      background: ${colors.textSecondary};
      cursor: not-allowed;
      opacity: 0.6;
    }

    .chatbot-trigger-button {
      position: fixed;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      border: none;
      background-color: ${colors.primary};
      color: ${colors.textOnPrimary};
      font-size: 24px;
      cursor: pointer;
      box-shadow: 0 4px 20px ${colors.shadow};
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: 0.3s ease;
    }

    .chatbot-trigger-button:hover {
      background-color: ${colors.primaryHover};
      transform: scale(1.05);
    }

    .chatbot-trigger-button.active {
      background-color: ${colors.primaryActive};
      transform: scale(0.95);
    }

    .suggested-actions {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 12px;
      justify-content: center;
    }

    .suggested-action-button {
      background: ${colors.surface};
      color: ${colors.text};
      border: 1px solid ${colors.border};
      border-radius: 16px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: 0.15s ease;
      font-family: "Etelka", -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .suggested-action-button:hover {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
      border-color: ${colors.primary};
    }

    .suggested-action-button:active {
      background: ${colors.primaryActive};
      transform: scale(0.98);
    }

    .icon-button {
      background: none;
      border: none;
      color: ${colors.textOnPrimary};
      cursor: pointer;
      padding: 6px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.15s ease;
      width: 32px;
      height: 32px;
    }

    .icon-button:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: scale(1.05);
    }

    .icon-button:active {
      background: rgba(255, 255, 255, 0.15);
      transform: scale(0.95);
    }

    .icon-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .icon-button:disabled:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: none;
    }

    .icon-button.primary {
      background: ${colors.primary};
      color: ${colors.textOnPrimary};
      border: none;
    }

    .icon-button.primary:hover {
      background: ${colors.primaryHover};
    }

    .icon-button.primary:active {
      background: ${colors.primaryActive};
    }

    .icon-button.primary:disabled {
      background: ${colors.textSecondary};
      opacity: 0.6;
    }

    .markdown-link {
      color: ${colors.primary};
      text-decoration: underline;
      text-decoration-color: ${colors.primary};
      text-underline-offset: 2px;
      transition: all 0.15s ease;
      cursor: pointer;
    }

    .markdown-link:hover {
      color: ${colors.primaryHover};
      text-decoration-color: ${colors.primaryHover};
      text-decoration-thickness: 2px;
    }

    .markdown-link:active {
      color: ${colors.primaryActive};
      text-decoration-color: ${colors.primaryActive};
    }

    .markdown-link:visited {
      color: ${colors.primary};
      opacity: 0.8;
    }

    /* ReactMarkdown styling */
    .message-content h1,
    .message-content h2,
    .message-content h3,
    .message-content h4,
    .message-content h5,
    .message-content h6 {
      color: ${colors.text};
      margin: 8px 0;
      font-family: "Etelka", -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    }

    .message-content h1 { font-size: 20px; font-weight: 900; }
    .message-content h2 { font-size: 18px; font-weight: 700; }
    .message-content h3 { font-size: 16px; font-weight: 700; }
    .message-content h4 { font-size: 14px; font-weight: 500; }
    .message-content h5 { font-size: 13px; font-weight: 500; }
    .message-content h6 { font-size: 12px; font-weight: 500; }

    .message-content p {
      margin: 4px 0;
      line-height: 1.4;
    }

    .message-content ul,
    .message-content ol {
      margin: 8px 0;
      padding-left: 16px;
    }

    .message-content li {
      margin: 2px 0;
    }

    .message-content blockquote {
      border-left: 3px solid ${colors.primary};
      margin: 8px 0;
      padding: 4px 0 4px 12px;
      font-style: italic;
      color: ${colors.textSecondary};
    }

    .message-content code {
      background: ${colors.surface};
      border: 1px solid ${colors.border};
      border-radius: 4px;
      padding: 2px 4px;
      font-family: 'Courier New', monospace;
      font-size: 13px;
      color: ${colors.text};
    }

    .message-content pre {
      background: ${colors.surface};
      border: 1px solid ${colors.border};
      border-radius: 6px;
      padding: 12px;
      margin: 8px 0;
      overflow-x: auto;
      font-family: 'Courier New', monospace;
      font-size: 13px;
    }

    .message-content pre code {
      background: none;
      border: none;
      padding: 0;
    }

    .message-content em {
      font-style: italic;
    }

    .message-content strong {
      font-weight: 700;
    }

    /* Fullpage mode backdrop effect */
    .chatbot-container.fullpage::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(2px);
      z-index: -1;
      opacity: 0;
      transition: opacity 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
      animation: fadeInBackdrop 0.4s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
    }

    @keyframes fadeInBackdrop {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }

    /* Enhanced focus states for fullpage mode */
    .chatbot-container.fullpage .chatbot-input {
      font-size: 16px;
    }

    .chatbot-container.fullpage .message-content {
      font-size: 15px;
      line-height: 1.5;
    }

    /* Smooth scaling animation for maximize/minimize button */
    .chatbot-header .icon-button {
      transition: transform 0.2s cubic-bezier(0.2, 0.8, 0.2, 1);
    }

    .chatbot-header .icon-button:active {
      transform: scale(0.95);
    }
  `;
}
