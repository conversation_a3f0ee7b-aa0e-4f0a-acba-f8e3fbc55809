import { type Theme } from './themes';
import { type Namespace } from '../types';
import { defaultTheme, darkTheme, blueTheme, greenTheme, purpleTheme } from './themes';

export const namespaceThemeMap: Record<Namespace, Theme> = {
  'light-namespace': defaultTheme,
  'dark-namespace': darkTheme,
  'blue-namespace': blueTheme,
  'green-namespace': greenTheme,
  'purple-namespace': purpleTheme,
};

export function getThemeByNamespace(namespace: Namespace): Theme {
  return namespaceThemeMap[namespace];
}

export function getNamespaceFromTheme(themeName: string): Namespace {
  switch (themeName) {
    case 'light': return 'light-namespace';
    case 'dark': return 'dark-namespace';
    case 'blue': return 'blue-namespace';
    case 'green': return 'green-namespace';
    case 'purple': return 'purple-namespace';
    default: return 'light-namespace';
  }
}