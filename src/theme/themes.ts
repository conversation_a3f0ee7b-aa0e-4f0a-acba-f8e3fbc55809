import { type DesignTokens, baseTokens } from './tokens';

export type ThemeVariant = 'light' | 'dark';

export interface Theme {
  name: string;
  variant: ThemeVariant;
  tokens: DesignTokens;
}

// Light theme (default)
export const lightTheme: Theme = {
  name: 'Light',
  variant: 'light',
  tokens: baseTokens,
};

// Dark theme
export const darkTheme: Theme = {
  name: 'Dark',
  variant: 'dark',
  tokens: {
    ...baseTokens,
    colors: {
      ...baseTokens.colors,
      primary: '#5aa3f0',
      primaryHover: '#4a90e2',
      primaryActive: '#3a7bd5',
      background: '#1a1a1a',
      surface: '#2d2d2d',
      surfaceHover: '#404040',
      text: '#ffffff',
      textSecondary: '#b0b0b0',
      textOnPrimary: '#ffffff',
      border: '#404040',
      shadow: 'rgba(0, 0, 0, 0.3)',
    },
  },
};

// Blue theme variant
export const blueTheme: Theme = {
  name: 'Blue',
  variant: 'light',
  tokens: {
    ...baseTokens,
    colors: {
      ...baseTokens.colors,
      primary: '#007bff',
      primaryHover: '#0056b3',
      primaryActive: '#004085',
      surface: '#f0f8ff',
      border: '#b3d9ff',
    },
  },
};

// Green theme variant
export const greenTheme: Theme = {
  name: 'Green',
  variant: 'light',
  tokens: {
    ...baseTokens,
    colors: {
      ...baseTokens.colors,
      primary: '#28a745',
      primaryHover: '#218838',
      primaryActive: '#1e7e34',
      surface: '#f0fff4',
      border: '#b3e6cc',
    },
  },
};

// Purple theme variant
export const purpleTheme: Theme = {
  name: 'Purple',
  variant: 'light',
  tokens: {
    ...baseTokens,
    colors: {
      ...baseTokens.colors,
      primary: '#6f42c1',
      primaryHover: '#5a32a3',
      primaryActive: '#4c2a85',
      surface: '#f8f0ff',
      border: '#d1b3e6',
    },
  },
};

// Available themes
export const themes = {
  light: lightTheme,
  dark: darkTheme,
  blue: blueTheme,
  green: greenTheme,
  purple: purpleTheme,
} as const;

export type ThemeName = keyof typeof themes;

// Default theme
export const defaultTheme = lightTheme;

// Theme utilities
export const getTheme = (themeName: ThemeName): Theme => {
  return themes[themeName] || defaultTheme;
};

export const getThemeNames = (): ThemeName[] => {
  return Object.keys(themes) as ThemeName[];
};