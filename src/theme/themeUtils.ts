import { type Theme } from './themes';

// Generate CSS variables from theme tokens
export function generateThemeVariables(theme: Theme): string {
  const { tokens } = theme;
  
  const cssVars = [
    // Colors
    `--color-primary: ${tokens.colors.primary};`,
    `--color-primary-hover: ${tokens.colors.primaryHover};`,
    `--color-primary-active: ${tokens.colors.primaryActive};`,
    `--color-secondary: ${tokens.colors.secondary};`,
    `--color-background: ${tokens.colors.background};`,
    `--color-surface: ${tokens.colors.surface};`,
    `--color-surface-hover: ${tokens.colors.surfaceHover};`,
    `--color-text: ${tokens.colors.text};`,
    `--color-text-secondary: ${tokens.colors.textSecondary};`,
    `--color-text-on-primary: ${tokens.colors.textOnPrimary};`,
    `--color-border: ${tokens.colors.border};`,
    `--color-shadow: ${tokens.colors.shadow};`,
    `--color-success: ${tokens.colors.success};`,
    `--color-warning: ${tokens.colors.warning};`,
    `--color-error: ${tokens.colors.error};`,
    
    // Spacing
    `--spacing-xs: ${tokens.spacing.xs};`,
    `--spacing-sm: ${tokens.spacing.sm};`,
    `--spacing-md: ${tokens.spacing.md};`,
    `--spacing-lg: ${tokens.spacing.lg};`,
    `--spacing-xl: ${tokens.spacing.xl};`,
    `--spacing-xxl: ${tokens.spacing.xxl};`,
    
    // Border radius
    `--border-radius-sm: ${tokens.borderRadius.sm};`,
    `--border-radius-md: ${tokens.borderRadius.md};`,
    `--border-radius-lg: ${tokens.borderRadius.lg};`,
    `--border-radius-xl: ${tokens.borderRadius.xl};`,
    `--border-radius-full: ${tokens.borderRadius.full};`,
    
    // Typography
    `--font-family: ${tokens.typography.fontFamily};`,
    `--font-size-xs: ${tokens.typography.fontSize.xs};`,
    `--font-size-sm: ${tokens.typography.fontSize.sm};`,
    `--font-size-md: ${tokens.typography.fontSize.md};`,
    `--font-size-lg: ${tokens.typography.fontSize.lg};`,
    `--font-size-xl: ${tokens.typography.fontSize.xl};`,
    `--font-weight-light: ${tokens.typography.fontWeight.light};`,
    `--font-weight-normal: ${tokens.typography.fontWeight.normal};`,
    `--font-weight-medium: ${tokens.typography.fontWeight.medium};`,
    `--font-weight-bold: ${tokens.typography.fontWeight.bold};`,
    `--font-weight-black: ${tokens.typography.fontWeight.black};`,
    `--line-height-tight: ${tokens.typography.lineHeight.tight};`,
    `--line-height-normal: ${tokens.typography.lineHeight.normal};`,
    `--line-height-relaxed: ${tokens.typography.lineHeight.relaxed};`,
    
    // Shadows
    `--shadow-sm: ${tokens.shadows.sm};`,
    `--shadow-md: ${tokens.shadows.md};`,
    `--shadow-lg: ${tokens.shadows.lg};`,
    `--shadow-xl: ${tokens.shadows.xl};`,
    
    // Z-index
    `--z-index-dropdown: ${tokens.zIndex.dropdown};`,
    `--z-index-sticky: ${tokens.zIndex.sticky};`,
    `--z-index-fixed: ${tokens.zIndex.fixed};`,
    `--z-index-modal: ${tokens.zIndex.modal};`,
    `--z-index-popover: ${tokens.zIndex.popover};`,
    `--z-index-tooltip: ${tokens.zIndex.tooltip};`,
    
    // Transitions
    `--transition-fast: ${tokens.transitions.fast};`,
    `--transition-normal: ${tokens.transitions.normal};`,
    `--transition-slow: ${tokens.transitions.slow};`,
  ];
  
  return `:root { ${cssVars.join(' ')} }`;
}