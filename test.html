<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chatbot Widget - Consolidated Test Suite</title>
    <style>
      /* Host page styles to test isolation */
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 0;
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        min-height: 100vh;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
      }

      .header {
        background: #fff;
        padding: 20px;
        border-bottom: 2px solid #eee;
        text-align: center;
      }

      .header h1 {
        color: #333;
        margin: 0;
        font-size: 2.5em;
      }

      .header p {
        color: #666;
        margin: 10px 0 0;
        font-size: 1.1em;
      }

      /* Tab Navigation */
      .tabs {
        display: flex;
        background: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        overflow-x: auto;
      }

      .tab {
        padding: 15px 25px;
        cursor: pointer;
        border: none;
        background: none;
        font-size: 14px;
        font-weight: 600;
        color: #6c757d;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
        white-space: nowrap;
      }

      .tab:hover {
        background: #e9ecef;
        color: #495057;
      }

      .tab.active {
        color: #007bff;
        border-bottom-color: #007bff;
        background: white;
      }

      .tab-content {
        display: none;
        padding: 30px;
        animation: fadeIn 0.3s ease;
      }

      .tab-content.active {
        display: block;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      /* Status indicators */
      .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
      }

      .status-card {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
      }

      .status-card h4 {
        margin: 0 0 8px;
        color: #495057;
        font-size: 14px;
      }

      .status-indicator {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
        text-transform: uppercase;
      }

      .status-connected { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
      .status-disconnected { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
      .status-pending { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
      .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }

      /* Controls */
      .controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
      }

      .btn {
        padding: 12px 18px;
        border: none;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        text-align: center;
      }

      .btn-primary { background: #007bff; color: white; }
      .btn-primary:hover { background: #0056b3; transform: translateY(-2px); }
      
      .btn-success { background: #28a745; color: white; }
      .btn-success:hover { background: #218838; transform: translateY(-2px); }
      
      .btn-warning { background: #ffc107; color: #212529; }
      .btn-warning:hover { background: #e0a800; transform: translateY(-2px); }
      
      .btn-danger { background: #dc3545; color: white; }
      .btn-danger:hover { background: #c82333; transform: translateY(-2px); }

      .btn-secondary { background: #6c757d; color: white; }
      .btn-secondary:hover { background: #545b62; transform: translateY(-2px); }

      /* Output areas */
      .output {
        background: #263238;
        color: #4fc3f7;
        border-radius: 8px;
        padding: 20px;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        line-height: 1.4;
        margin-top: 20px;
      }

      .theme-preview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }

      .theme-card {
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        cursor: pointer;
        transition: transform 0.3s ease;
        border: 2px solid transparent;
      }

      .theme-card:hover {
        transform: scale(1.05);
        border-color: #007bff;
      }

      .theme-card.active {
        border-color: #28a745;
        box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
      }

      /* Theme-specific colors for preview */
      .theme-light { background: #f8f9fa; color: #212529; }
      .theme-dark { background: #343a40; color: #f8f9fa; }
      .theme-blue { background: #e3f2fd; color: #0d47a1; }
      .theme-green { background: #e8f5e8; color: #2e7d32; }
      .theme-purple { background: #f3e5f5; color: #7b1fa2; }

      /* Information sections */
      .info-section {
        background: #e7f3ff;
        border: 2px solid #b3d9ff;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
      }

      .info-section h3 {
        color: #0056b3;
        margin: 0 0 10px;
      }

      .info-section ul {
        margin: 0;
        padding-left: 20px;
      }

      .info-section li {
        margin-bottom: 5px;
        color: #004085;
      }

      /* These styles should NOT affect the chatbot widget due to Shadow DOM */
      .should-not-affect-widget * {
        background: red !important;
        color: yellow !important;
        font-size: 50px !important;
        border: 10px solid purple !important;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🤖 Chatbot Widget Test Suite</h1>
        <p>Comprehensive testing for React Chatbot Widget with Bot Framework integration</p>
      </div>

      <!-- Tab Navigation -->
      <div class="tabs">
        <button class="tab active" onclick="switchTab('basic')">🎨 Basic Widget</button>
        <button class="tab" onclick="switchTab('ui-components')">🧩 UI Components</button>
        <button class="tab" onclick="switchTab('bot-framework')">🤖 Bot Framework</button>
        <button class="tab" onclick="switchTab('proxy-integration')">🔒 Proxy Integration</button>
        <button class="tab" onclick="switchTab('session-persistence')">🗄️ Session Persistence</button>
        <button class="tab" onclick="switchTab('theme-testing')">🎨 Theme Testing</button>
        <button class="tab" onclick="switchTab('api-testing')">🔗 API Testing</button>
      </div>

      <!-- Basic Widget Testing -->
      <div id="basic-tab" class="tab-content active">
        <h2>🎨 Basic Widget Testing</h2>
        
        <div class="info-section">
          <h3>🎯 Test Objectives</h3>
          <ul>
            <li>Shadow DOM isolation verification</li>
            <li>Basic widget initialization</li>
            <li>Theme switching functionality</li>
            <li>Position and visibility controls</li>
          </ul>
        </div>

        <div class="status-grid">
          <div class="status-card">
            <h4>Widget Status</h4>
            <div id="basic-widget-status" class="status-indicator status-disconnected">Not Initialized</div>
          </div>
          <div class="status-card">
            <h4>Theme Status</h4>
            <div id="basic-theme-status" class="status-indicator status-disconnected">Default</div>
          </div>
          <div class="status-card">
            <h4>Position</h4>
            <div id="basic-position-status" class="status-indicator status-pending">Right Bottom</div>
          </div>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="basicTests.initialize()">Initialize Widget</button>
          <button class="btn btn-success" onclick="basicTests.open()">Open Widget</button>
          <button class="btn btn-warning" onclick="basicTests.close()">Close Widget</button>
          <button class="btn btn-secondary" onclick="basicTests.testThemes()">Test All Themes</button>
          <button class="btn btn-danger" onclick="basicTests.destroy()">Destroy Widget</button>
        </div>

        <div class="theme-preview">
          <div class="theme-card theme-light" onclick="basicTests.setTheme('light-namespace')">
            <h4>Light</h4>
            <p>Default theme</p>
          </div>
          <div class="theme-card theme-dark" onclick="basicTests.setTheme('dark-namespace')">
            <h4>Dark</h4>
            <p>High contrast</p>
          </div>
          <div class="theme-card theme-blue" onclick="basicTests.setTheme('blue-namespace')">
            <h4>Blue</h4>
            <p>Professional</p>
          </div>
          <div class="theme-card theme-green" onclick="basicTests.setTheme('green-namespace')">
            <h4>Green</h4>
            <p>Nature-inspired</p>
          </div>
          <div class="theme-card theme-purple" onclick="basicTests.setTheme('purple-namespace')">
            <h4>Purple</h4>
            <p>Creative</p>
          </div>
        </div>

        <div id="basic-output" class="output">Ready for basic widget testing...
Click "Initialize Widget" to start testing.

Test Steps:
1. Initialize Widget - Creates basic chatbot
2. Open Widget - Shows chat interface
3. Test themes - Switch between 5 themes
4. Close Widget - Minimizes interface
5. Destroy Widget - Removes completely
</div>
      </div>

      <!-- UI Components Testing -->
      <div id="ui-components-tab" class="tab-content">
        <h2>🧩 UI Components Testing</h2>

        <div class="info-section">
          <h3>🧪 Component Test Coverage</h3>
          <ul>
            <li>Avatar component (Eva image + user icon)</li>
            <li>Message timestamp formatting</li>
            <li>Message interface and layout</li>
            <li>Button positioning and styles</li>
            <li>Shadow DOM style isolation</li>
          </ul>
        </div>

        <div class="status-grid">
          <div class="status-card">
            <h4>Avatar Loading</h4>
            <div id="ui-avatar-status" class="status-indicator status-pending">Not Tested</div>
          </div>
          <div class="status-card">
            <h4>Timestamps</h4>
            <div id="ui-timestamp-status" class="status-indicator status-pending">Not Tested</div>
          </div>
          <div class="status-card">
            <h4>Style Isolation</h4>
            <div id="ui-isolation-status" class="status-indicator status-pending">Not Tested</div>
          </div>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="uiTests.initializeForTesting()">Initialize for UI Testing</button>
          <button class="btn btn-success" onclick="uiTests.testAvatars()">Test Avatars</button>
          <button class="btn btn-warning" onclick="uiTests.testTimestamps()">Test Timestamps</button>
          <button class="btn btn-secondary" onclick="uiTests.testStyleIsolation()">Test Style Isolation</button>
          <button class="btn btn-primary" onclick="uiTests.testMessageInterface()">Test Message Interface</button>
        </div>

        <div class="should-not-affect-widget">
          <p>This section has aggressive CSS styles that should NOT affect the widget due to Shadow DOM isolation</p>
        </div>

        <div id="ui-output" class="output">UI Components Test Console...

Available Tests:
• Avatar Component - Eva image loading and user icon display
• Message Timestamps - HH:MM format verification
• Message Interface - Layout and styling tests
• Style Isolation - Verify Shadow DOM protection
• Button Positioning - Corner placement testing

Click "Initialize for UI Testing" to begin.
</div>
      </div>

      <!-- Bot Framework Testing -->
      <div id="bot-framework-tab" class="tab-content">
        <h2>🤖 Bot Framework Integration</h2>

        <div class="info-section">
          <h3>🔧 DirectLine Proxy Setup</h3>
          <ul>
            <li>Proxy Server: <code>cd directline-proxy-server && npm start</code> (Port 3001)</li>
            <li>DirectLine API proxy to Copilot Studio</li>
            <li>Real conversation testing with your Copilot Studio</li>
            <li>Session persistence and error handling</li>
          </ul>
        </div>

        <div class="status-grid">
          <div class="status-card">
            <h4>Proxy Server</h4>
            <div id="bot-server-status" class="status-indicator status-pending">Checking...</div>
          </div>
          <div class="status-card">
            <h4>Copilot Studio</h4>
            <div id="bot-directline-status" class="status-indicator status-disconnected">Not Connected</div>
          </div>
          <div class="status-card">
            <h4>Bot Messages</h4>
            <div id="bot-messages-status" class="status-indicator status-pending">No Messages</div>
          </div>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="botTests.checkServer()">Check Proxy Server</button>
          <button class="btn btn-success" onclick="botTests.initialize()">Connect to Copilot Studio</button>
          <button class="btn btn-warning" onclick="botTests.testConversation()">Test Real Conversation</button>
          <button class="btn btn-secondary" onclick="botTests.testErrorHandling()">Test Error Handling</button>
        </div>

        <div id="bot-output" class="output">DirectLine Proxy Integration Test Console...

Prerequisites:
1. Proxy server running on localhost:3001
2. Widget built with 'npm run build'
3. Your Copilot Studio DirectLine secret configured

Click "Check Proxy Server" to verify setup.
</div>
      </div>

      <!-- Proxy Integration Testing -->
      <div id="proxy-integration-tab" class="tab-content">
        <h2>🔒 Secure Proxy Integration</h2>

        <div class="info-section">
          <h3>🛡️ Proxy Architecture</h3>
          <ul>
            <li>Proxy Server: <code>cd directline-proxy-server && npm start</code> (Port 3001)</li>
            <li>Secure token management</li>
            <li>DirectLine API proxying</li>
            <li>Rate limiting and security</li>
          </ul>
        </div>

        <div class="status-grid">
          <div class="status-card">
            <h4>Proxy Server</h4>
            <div id="proxy-server-status" class="status-indicator status-pending">Checking...</div>
          </div>
          <div class="status-card">
            <h4>Token Service</h4>
            <div id="proxy-token-status" class="status-indicator status-disconnected">No Token</div>
          </div>
          <div class="status-card">
            <h4>Secure Connection</h4>
            <div id="proxy-connection-status" class="status-indicator status-disconnected">Not Connected</div>
          </div>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="proxyTests.checkHealth()">Check Proxy Health</button>
          <button class="btn btn-success" onclick="proxyTests.testToken()">Generate Token</button>
          <button class="btn btn-warning" onclick="proxyTests.initializeWidget()">Initialize with Proxy</button>
          <button class="btn btn-secondary" onclick="proxyTests.checkStats()">Check Statistics</button>
        </div>

        <div id="proxy-output" class="output">Secure Proxy Integration Test Console...

Configuration:
• Proxy URL: http://localhost:3001
• Security: Token-based authentication
• Features: Rate limiting, logging, caching

Click "Check Proxy Health" to start testing.
</div>
      </div>

      <!-- Session Persistence Testing -->
      <div id="session-persistence-tab" class="tab-content">
        <h2>🗄️ Session Persistence Testing</h2>

        <div class="info-section">
          <h3>💾 Session Features</h3>
          <ul>
            <li>24-hour localStorage persistence</li>
            <li>Bot Framework data storage (tokens, URLs)</li>
            <li>Visibility state persistence</li>
            <li>Custom data storage</li>
          </ul>
        </div>

        <div class="status-grid">
          <div class="status-card">
            <h4>Session Active</h4>
            <div id="session-active-status" class="status-indicator status-pending">Checking...</div>
          </div>
          <div class="status-card">
            <h4>Data Stored</h4>
            <div id="session-data-status" class="status-indicator status-disconnected">No Data</div>
          </div>
          <div class="status-card">
            <h4>Persistence</h4>
            <div id="session-persistence-status" class="status-indicator status-pending">Not Tested</div>
          </div>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="sessionTests.initialize()">Initialize Session</button>
          <button class="btn btn-success" onclick="sessionTests.storeExampleData()">Store Bot Framework Data</button>
          <button class="btn btn-warning" onclick="sessionTests.retrieveData()">Retrieve Session Data</button>
          <button class="btn btn-secondary" onclick="sessionTests.testPersistence()">Test Persistence</button>
          <button class="btn btn-danger" onclick="sessionTests.clearSession()">Clear Session</button>
        </div>

        <div id="session-output" class="output">Session Persistence Test Console...

Session Data API:
• setSessionData() - Store Bot Framework session data
• getSessionData() - Retrieve persisted data  
• clearSession() - Remove session storage

Click "Initialize Session" to start testing.
</div>
      </div>

      <!-- Theme Testing -->
      <div id="theme-testing-tab" class="tab-content">
        <h2>🎨 Theme System Testing</h2>

        <div class="info-section">
          <h3>🌈 Theme Features</h3>
          <ul>
            <li>5 namespace-based themes (light, dark, blue, green, purple)</li>
            <li>Real-time theme switching</li>
            <li>Design token system</li>
            <li>Shadow DOM CSS injection</li>
          </ul>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="themeTests.initializeThemeTesting()">Initialize Theme Testing</button>
          <button class="btn btn-success" onclick="themeTests.cycleAllThemes()">Cycle All Themes</button>
          <button class="btn btn-warning" onclick="themeTests.testRapidSwitching()">Test Rapid Switching</button>
          <button class="btn btn-secondary" onclick="themeTests.verifyIsolation()">Verify CSS Isolation</button>
        </div>

        <div class="theme-preview">
          <div class="theme-card theme-light" onclick="themeTests.setTheme('light-namespace')">
            <h4>Light Theme</h4>
            <div style="background: #f8f9fa; color: #212529; padding: 10px; border-radius: 4px; margin: 5px 0;">
              Sample colors
            </div>
          </div>
          <div class="theme-card theme-dark" onclick="themeTests.setTheme('dark-namespace')">
            <h4>Dark Theme</h4>
            <div style="background: #343a40; color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0;">
              Sample colors
            </div>
          </div>
          <div class="theme-card theme-blue" onclick="themeTests.setTheme('blue-namespace')">
            <h4>Blue Theme</h4>
            <div style="background: #e3f2fd; color: #0d47a1; padding: 10px; border-radius: 4px; margin: 5px 0;">
              Sample colors
            </div>
          </div>
          <div class="theme-card theme-green" onclick="themeTests.setTheme('green-namespace')">
            <h4>Green Theme</h4>
            <div style="background: #e8f5e8; color: #2e7d32; padding: 10px; border-radius: 4px; margin: 5px 0;">
              Sample colors
            </div>
          </div>
          <div class="theme-card theme-purple" onclick="themeTests.setTheme('purple-namespace')">
            <h4>Purple Theme</h4>
            <div style="background: #f3e5f5; color: #7b1fa2; padding: 10px; border-radius: 4px; margin: 5px 0;">
              Sample colors
            </div>
          </div>
        </div>

        <div id="theme-output" class="output">Theme Testing Console...

Available Themes:
• light-namespace - Minimal, clean design
• dark-namespace - High contrast for accessibility  
• blue-namespace - Professional corporate theme
• green-namespace - Nature-inspired calming theme
• purple-namespace - Creative and modern theme

Click "Initialize Theme Testing" to begin.
</div>
      </div>

      <!-- API Testing -->
      <div id="api-testing-tab" class="tab-content">
        <h2>🔗 API Testing</h2>

        <div class="info-section">
          <h3>📡 API Methods</h3>
          <ul>
            <li>Message sending and receiving</li>
            <li>Variable setting and retrieval</li>
            <li>Event listening and handling</li>
            <li>State management</li>
          </ul>
        </div>

        <div class="controls">
          <button class="btn btn-primary" onclick="apiTests.initialize()">Initialize API Testing</button>
          <button class="btn btn-success" onclick="apiTests.sendMessage()">Send Test Message</button>
          <button class="btn btn-warning" onclick="apiTests.setVariable()">Set Variables</button>
          <button class="btn btn-secondary" onclick="apiTests.testEvents()">Test Events</button>
          <button class="btn btn-primary" onclick="apiTests.getState()">Get Current State</button>
          <button class="btn btn-success" onclick="apiTests.testMarkdownLinks()">Test Markdown Links</button>
        </div>

        <div id="api-output" class="output">API Testing Console...

API Methods Available:
• sendMessage - Send user messages
• setVariable - Set context variables
• setUserInfo - Update user information
• setTopic - Change conversation topic
• on/off - Event listener management
• getState - Retrieve current state

Click "Initialize API Testing" to start.
</div>
      </div>
    </div>

    <!-- Load the chatbot widget -->
    <script src="dist/chatbot-widget.iife.js"></script>
    <script>
      // Global state for testing
      let currentTab = 'basic';
      let widgetInitialized = false;
      let currentTheme = 'light-namespace';

      // Utility functions
      function log(tabId, message, type = 'info') {
        const output = document.getElementById(`${tabId}-output`);
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
        output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
        output.scrollTop = output.scrollHeight;
      }

      function updateStatus(elementId, status, text = '') {
        const element = document.getElementById(elementId);
        if (element) {
          element.className = `status-indicator status-${status}`;
          element.textContent = text || status;
        }
      }

      function switchTab(tabName) {
        // Hide all tab content
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        
        // Remove active class from all tabs
        document.querySelectorAll('.tab').forEach(tab => {
          tab.classList.remove('active');
        });

        // Show selected tab
        document.getElementById(`${tabName}-tab`).classList.add('active');
        event.target.classList.add('active');
        currentTab = tabName;
      }

      // Basic Widget Tests
      const basicTests = {
        initialize() {
          log('basic', 'Initializing basic chatbot widget...');
          try {
            ChatbotWidget.push({
              action: 'init',
              args: {
                namespace: 'light-namespace',
                context: {
                  variables: { 
                    name: 'Test User',
                    event: 'Basic Widget Test'
                  }
                },
                visibility: 'minimized'
              }
            });
            updateStatus('basic-widget-status', 'connected', 'Initialized');
            updateStatus('basic-theme-status', 'connected', 'Light Theme');
            log('basic', 'Widget initialized successfully!', 'success');
            widgetInitialized = true;
          } catch (error) {
            log('basic', `Initialization failed: ${error.message}`, 'error');
            updateStatus('basic-widget-status', 'error', 'Failed');
          }
        },

        open() {
          if (!widgetInitialized) {
            log('basic', 'Initialize widget first', 'warning');
            return;
          }
          ChatbotWidget.push({ action: 'open' });
          log('basic', 'Widget opened', 'success');
        },

        close() {
          if (!widgetInitialized) {
            log('basic', 'Initialize widget first', 'warning');
            return;
          }
          ChatbotWidget.push({ action: 'close' });
          log('basic', 'Widget closed', 'success');
        },

        destroy() {
          ChatbotWidget.push({ action: 'destroy' });
          updateStatus('basic-widget-status', 'disconnected', 'Destroyed');
          log('basic', 'Widget destroyed', 'success');
          widgetInitialized = false;
        },

        setTheme(namespace) {
          if (!widgetInitialized) {
            this.initialize();
            setTimeout(() => this.actuallySetTheme(namespace), 500);
          } else {
            this.actuallySetTheme(namespace);
          }
        },

        actuallySetTheme(namespace) {
          ChatbotWidget.push({
            action: 'init',
            args: {
              namespace: namespace,
              context: {
                variables: { 
                  name: 'Theme Tester',
                  event: `${namespace} Theme Test`
                }
              },
              visibility: 'open'
            }
          });
          currentTheme = namespace;
          updateStatus('basic-theme-status', 'connected', namespace.replace('-namespace', ''));
          log('basic', `Theme switched to ${namespace}`, 'success');

          // Update theme card active state
          document.querySelectorAll('.theme-card').forEach(card => card.classList.remove('active'));
          document.querySelector(`.theme-card.theme-${namespace.replace('-namespace', '')}`).classList.add('active');
        },

        testThemes() {
          const themes = ['light-namespace', 'dark-namespace', 'blue-namespace', 'green-namespace', 'purple-namespace'];
          let index = 0;
          
          const cycleThem = () => {
            if (index < themes.length) {
              this.setTheme(themes[index]);
              log('basic', `Testing theme ${index + 1}/5: ${themes[index]}`, 'info');
              index++;
              setTimeout(cycleThem, 2000);
            } else {
              log('basic', 'All themes tested successfully!', 'success');
            }
          };
          
          cycleThem();
        }
      };

      // UI Components Tests
      const uiTests = {
        initializeForTesting() {
          log('ui-components', 'Initializing widget for UI component testing...');
          basicTests.initialize();
          setTimeout(() => {
            ChatbotWidget.push({ action: 'open' });
            log('ui-components', 'Widget opened for UI inspection', 'success');
          }, 500);
        },

        testAvatars() {
          log('ui-components', 'Testing avatar components...');
          log('ui-components', 'Check widget for Eva bot avatar (eva.png)', 'info');
          log('ui-components', 'Check widget for user icon placeholder', 'info');
          updateStatus('ui-avatar-status', 'connected', 'Visible');
          
          // Send a test message to show both avatars
          setTimeout(() => {
            ChatbotWidget.push({
              action: 'sendMessage',
              args: { text: 'Test message to show avatars' }
            });
          }, 1000);
        },

        testTimestamps() {
          log('ui-components', 'Testing message timestamps (HH:MM format)...');
          updateStatus('ui-timestamp-status', 'connected', 'HH:MM Format');
          log('ui-components', 'Check messages for timestamp display above each message', 'info');
        },

        testStyleIsolation() {
          log('ui-components', 'Testing Shadow DOM style isolation...');
          log('ui-components', 'Red/yellow aggressive styles below should NOT affect widget', 'warning');
          updateStatus('ui-isolation-status', 'connected', 'Isolated');
          log('ui-components', 'Verify widget maintains its theme colors', 'info');
        },

        testMessageInterface() {
          log('ui-components', 'Testing message interface layout...');
          ChatbotWidget.push({
            action: 'sendMessage',
            args: { text: 'Testing message interface with timestamp and avatar layout' }
          });
          log('ui-components', 'Message sent to test interface layout', 'success');
        }
      };

      // Bot Framework Tests
      const botTests = {
        async checkServer() {
          log('bot-framework', 'Checking mock Bot Framework server...');
          updateStatus('bot-server-status', 'pending', 'Checking...');
          
          try {
            const response = await fetch('http://localhost:3001/health');
            if (response.ok) {
              const data = await response.json();
              log('bot-framework', `Mock server is running: ${data.status}`, 'success');
              log('bot-framework', `Conversations: ${data.conversations}, Activities: ${data.totalActivities}`);
              updateStatus('bot-server-status', 'connected', 'Port 3978');
            } else {
              throw new Error(`Server responded with ${response.status}`);
            }
          } catch (error) {
            log('bot-framework', `Mock server connection failed: ${error.message}`, 'error');
            updateStatus('bot-server-status', 'error', 'Offline');
          }
        },

        initialize() {
          log('bot-framework', 'Initializing Bot Framework integration...');
          
          // Set up event listeners for Bot Framework events
          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'widget.initialized',
              callback: (state) => {
                log('bot-framework', 'Widget initialized successfully', 'success');
                updateStatus('bot-directline-status', 'connected', 'Connected');
              }
            }
          });

          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'message.received',
              callback: (message) => {
                log('bot-framework', `Bot message received: "${message.text}"`, 'success');
                updateStatus('bot-messages-status', 'connected', 'Active');
              }
            }
          });

          // Initialize widget with Bot Framework configuration
          // This will use BotFrameworkProvider due to authToken
          ChatbotWidget.push({
            action: 'init',
            args: {
              namespace: 'green-namespace',
              proxyConfig: {
                proxyUrl: 'http://localhost:3001',
                debug: true
              },
              context: {
                variables: { 
                  name: 'Bot Framework Tester',
                  event: 'DirectLine Integration Test'
                }
              },
              visibility: 'open'
            }
          });

          log('bot-framework', 'Widget initialized with proxy configuration', 'success');
          log('bot-framework', 'Using DirectLine proxy: http://localhost:3001', 'info');
        },

        testConversation() {
          log('bot-framework', 'Testing conversation flow through widget...');
          
          // Send a test message through the widget API
          ChatbotWidget.push({
            action: 'sendMessage',
            args: {
              text: 'Hello Bot Framework! This is a test message.',
              metadata: { source: 'bot-framework-test', timestamp: Date.now() }
            }
          });

          log('bot-framework', 'Test message sent through widget API', 'success');
          log('bot-framework', 'Bot should respond automatically via DirectLine', 'info');
          
          // The message.received event listener will capture the bot response
          setTimeout(() => {
            log('bot-framework', 'Check widget for bot response...', 'info');
          }, 2000);
        },

        testErrorHandling() {
          log('bot-framework', 'Testing error handling scenarios...');
          
          // Test invalid server
          fetch('http://localhost:9999/invalid')
            .catch(error => {
              log('bot-framework', `✓ Connection error handling: ${error.message}`, 'success');
            });
            
          log('bot-framework', 'Error handling tests initiated', 'info');
        }
      };

      // Proxy Integration Tests  
      const proxyTests = {
        async checkHealth() {
          log('proxy', 'Checking proxy server health...');
          updateStatus('proxy-server-status', 'pending', 'Checking...');
          
          try {
            const response = await fetch('http://localhost:3001/health');
            if (response.ok) {
              const data = await response.json();
              log('proxy', `Proxy server: ${data.status} (${data.environment})`, 'success');
              updateStatus('proxy-server-status', 'connected', 'Healthy');
            } else {
              throw new Error(`Server responded with ${response.status}`);
            }
          } catch (error) {
            log('proxy', `Proxy connection failed: ${error.message}`, 'error');
            updateStatus('proxy-server-status', 'error', 'Offline');
          }
        },

        async testToken() {
          log('proxy', 'Testing token generation...');
          
          try {
            const response = await fetch('http://localhost:3001/api/auth/token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ userId: 'test-user', sessionId: `session_${Date.now()}` })
            });

            if (response.ok) {
              const tokenData = await response.json();
              log('proxy', `Token generated: expires in ${tokenData.expires_in}s`, 'success');
              updateStatus('proxy-token-status', 'connected', 'Valid');
            } else {
              const errorData = await response.json();
              throw new Error(errorData.message || response.statusText);
            }
          } catch (error) {
            log('proxy', `Token generation failed: ${error.message}`, 'error');
            updateStatus('proxy-token-status', 'error', 'Failed');
          }
        },

        initializeWidget() {
          log('proxy', 'Initializing widget with proxy configuration...');
          
          // Set up event listeners for proxy integration
          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'widget.initialized',
              callback: (state) => {
                log('proxy', 'Proxy widget initialized successfully', 'success');
                updateStatus('proxy-connection-status', 'connected', 'Via Proxy');
              }
            }
          });

          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'message.received',
              callback: (message) => {
                log('proxy', `Proxy bot message: "${message.text}"`, 'success');
              }
            }
          });
          
          // Initialize widget with proxy configuration
          // This will use ProxyBotFrameworkProvider due to proxyConfig
          ChatbotWidget.push({
            action: 'init',
            args: {
              namespace: 'blue-namespace',
              proxyConfig: {
                proxyUrl: 'http://localhost:3001',
                debug: true
              },
              context: {
                variables: { 
                  name: 'Proxy Tester',
                  event: 'Secure Proxy Test'
                }
              },
              visibility: 'open'
            }
          });

          log('proxy', 'Widget initialized with proxy config', 'success');
          log('proxy', 'Using proxy URL: http://localhost:3001', 'info');
        },

        async checkStats() {
          log('proxy', 'Checking proxy statistics...');
          
          try {
            const [authStats, convStats] = await Promise.all([
              fetch('http://localhost:3001/api/auth/stats'),
              fetch('http://localhost:3001/api/conversations/stats')
            ]);

            if (authStats.ok && convStats.ok) {
              const authData = await authStats.json();
              const convData = await convStats.json();
              
              log('proxy', `Tokens: ${authData.cache.totalTokens} (${authData.cache.activeTokens} active)`, 'success');
              log('proxy', `Conversations: ${convData.total_conversations}`, 'success');
            }
          } catch (error) {
            log('proxy', `Stats check failed: ${error.message}`, 'error');
          }
        }
      };

      // Session Persistence Tests
      const sessionTests = {
        initialize() {
          log('session-persistence', 'Initializing session for persistence testing...');
          
          ChatbotWidget.push({
            action: 'init',
            args: {
              namespace: 'purple-namespace',
              context: {
                variables: { 
                  name: 'Session Tester',
                  event: 'Session Persistence Test'
                }
              },
              visibility: 'minimized'
            }
          });

          updateStatus('session-active-status', 'connected', 'Active');
          log('session-persistence', 'Session initialized and should be persisted', 'success');
        },

        storeExampleData() {
          log('session-persistence', 'Storing Bot Framework session data...');
          
          const exampleData = {
            visibility: { window: "minimized" },
            options: { theme: "purple", customSetting: "test-value" },
            accessToken: "w2jZuMZPe3A8oiCgAV4uZt21HR0NO6SwBdrzQC04Axo4gFWBYGEm/LhDk/OY088XjiffbvZ35TEYaHItaryThw==",
            conversationUrl: "/channels/api/v2/client/3a3d68e0-0b67-488c-81d0-7a098d269017/srv_id/5010231/conversations/01K4286NNN6EJE5303SH0N14F2"
          };

          ChatbotWidget.push({
            action: 'setSessionData',
            args: exampleData
          });

          updateStatus('session-data-status', 'connected', 'Stored');
          log('session-persistence', 'Example Bot Framework data stored!', 'success');
          log('session-persistence', `Access token: ${exampleData.accessToken.substring(0, 30)}...`);
        },

        retrieveData() {
          log('session-persistence', 'Retrieving stored session data...');
          
          ChatbotWidget.push({
            action: 'getSessionData',
            callback: (sessionData) => {
              log('session-persistence', 'Session data retrieved:', 'success');
              log('session-persistence', `Data: ${JSON.stringify(sessionData, null, 2)}`);
              
              if (sessionData.accessToken) {
                log('session-persistence', `✓ Access token found: ${sessionData.accessToken.substring(0, 30)}...`);
              }
              if (sessionData.conversationUrl) {
                log('session-persistence', `✓ Conversation URL: ${sessionData.conversationUrl}`);
              }
              if (sessionData.visibility) {
                log('session-persistence', `✓ Visibility: ${JSON.stringify(sessionData.visibility)}`);
              }
            }
          });
        },

        testPersistence() {
          log('session-persistence', 'Testing localStorage persistence...');
          
          // Check localStorage directly
          const sessionData = localStorage.getItem('chatbot-session');
          if (sessionData) {
            const parsed = JSON.parse(sessionData);
            log('session-persistence', 'Session found in localStorage!', 'success');
            log('session-persistence', `Session ID: ${parsed.id}`);
            updateStatus('session-persistence-status', 'connected', 'Persisted');
          } else {
            log('session-persistence', 'No session data in localStorage', 'error');
            updateStatus('session-persistence-status', 'error', 'Not Found');
          }
        },

        clearSession() {
          log('session-persistence', 'Clearing session data...');
          
          ChatbotWidget.push({ action: 'clearSession' });
          
          updateStatus('session-active-status', 'disconnected', 'Cleared');
          updateStatus('session-data-status', 'disconnected', 'Empty');
          log('session-persistence', 'Session cleared from localStorage', 'success');
        }
      };

      // Theme Testing
      const themeTests = {
        initializeThemeTesting() {
          log('theme-testing', 'Initializing comprehensive theme testing...');
          basicTests.initialize();
          log('theme-testing', 'Ready for theme system testing', 'success');
        },

        setTheme(namespace) {
          basicTests.setTheme(namespace);
          log('theme-testing', `Applied ${namespace} theme`, 'success');
        },

        cycleAllThemes() {
          log('theme-testing', 'Cycling through all 5 themes...');
          basicTests.testThemes();
        },

        testRapidSwitching() {
          log('theme-testing', 'Testing rapid theme switching performance...');
          const themes = ['light-namespace', 'dark-namespace', 'blue-namespace', 'green-namespace', 'purple-namespace'];
          let count = 0;
          
          const rapidSwitch = () => {
            if (count < 20) {
              const randomTheme = themes[Math.floor(Math.random() * themes.length)];
              this.setTheme(randomTheme);
              count++;
              setTimeout(rapidSwitch, 200);
            } else {
              log('theme-testing', 'Rapid switching test completed!', 'success');
            }
          };
          
          rapidSwitch();
        },

        verifyIsolation() {
          log('theme-testing', 'Verifying CSS isolation between themes...', 'info');
          log('theme-testing', 'Host page styles should not affect widget themes', 'warning');
          log('theme-testing', 'Each theme should maintain distinct color schemes', 'info');
        }
      };

      // API Tests
      const apiTests = {
        initialize() {
          log('api-testing', 'Initializing API testing environment...');
          basicTests.initialize();
          
          // Set up event listeners
          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'message.sent',
              callback: (message) => {
                log('api-testing', `Event: message.sent - "${message.text}"`, 'success');
              }
            }
          });

          ChatbotWidget.push({
            action: 'on',
            args: {
              event: 'widget.opened',
              callback: () => {
                log('api-testing', 'Event: widget.opened', 'success');
              }
            }
          });

          log('api-testing', 'API testing environment ready', 'success');
        },

        sendMessage() {
          log('api-testing', 'Testing message sending API...');
          
          ChatbotWidget.push({
            action: 'sendMessage',
            args: {
              text: 'Test message from API',
              metadata: { source: 'api-test', timestamp: Date.now() }
            }
          });

          log('api-testing', 'Message sent via API', 'success');
        },

        testMarkdownLinks() {
          log('api-testing', 'Testing markdown link rendering...');
          
          // Send a message with markdown links to test rendering
          ChatbotWidget.push({
            action: 'sendMessage',
            args: {
              text: 'Here are some test links: [Claude AI](https://claude.ai) and [GitHub](https://github.com) and normal text.',
              metadata: { source: 'markdown-test', timestamp: Date.now() }
            }
          });

          // Send a full markdown message to test react-markdown
          setTimeout(() => {
            ChatbotWidget.push({
              action: 'sendMessage',
              args: {
                text: '# Markdown Test\n\nThis is **bold** and *italic* text with a [link](https://example.com).\n\n- List item 1\n- List item 2\n\n> This is a blockquote\n\n`inline code` and:\n\n```\ncode block\n```',
                textFormat: 'markdown',
                metadata: { source: 'full-markdown-test', timestamp: Date.now() }
              }
            });
            log('api-testing', 'Full markdown test message sent', 'success');
          }, 1000);

          log('api-testing', 'Markdown link test message sent', 'success');
          log('api-testing', 'Check widget for styled links with primary theme color', 'info');
        },

        setVariable() {
          log('api-testing', 'Testing variable setting...');
          
          ChatbotWidget.push({
            action: 'setVariable',
            args: {
              key: 'testVariable',
              value: 'API Test Value'
            }
          });

          ChatbotWidget.push({
            action: 'setUserInfo',
            args: {
              userInfo: { name: 'API Test User', role: 'Tester' }
            }
          });

          log('api-testing', 'Variables and user info updated', 'success');
        },

        testEvents() {
          log('api-testing', 'Testing event system...');
          
          // Open widget to trigger event
          ChatbotWidget.push({ action: 'open' });
          
          setTimeout(() => {
            ChatbotWidget.push({ action: 'close' });
          }, 1000);

          log('api-testing', 'Event triggers initiated', 'info');
        },

        getState() {
          log('api-testing', 'Getting current widget state...');
          
          ChatbotWidget.push({
            action: 'getState',
            callback: (state) => {
              log('api-testing', 'Current state retrieved:', 'success');
              log('api-testing', `State: ${JSON.stringify(state, null, 2)}`);
            }
          });
        }
      };

      // Auto-initialize
      document.addEventListener('DOMContentLoaded', () => {
        log(currentTab, 'Consolidated Test Suite Loaded');
        log(currentTab, 'Multiple test categories available in tabs above');
        
        // Auto-check for existing session
        setTimeout(() => {
          sessionTests.testPersistence();
        }, 500);
      });
    </script>
  </body>
</html>