# React Chatbot Widget

A professional React + TypeScript chatbot widget designed for embedding in any website with complete style isolation and comprehensive theming support.

## Features

- ✅ **Professional Design System** - 5 built-in themes with design tokens
- ✅ **Shadow DOM Isolation** - Complete style encapsulation 
- ✅ **DirectLine WebSocket Streaming** - Real-time Bot Framework integration
- ✅ **Interactive Elements** - Suggested actions and rich messaging
- ✅ **Session Persistence** - 24h localStorage with conversation state
- ✅ **Theme System** - Namespace-based theming (green, blue, purple, light, dark)
- ✅ **Auto Container Creation** - No HTML div required
- ✅ **Lazy Connection** - DirectLine only connects when user opens chat
- ✅ **Comprehensive API** - Full programmatic control
- ✅ **TypeScript** - Complete type safety and IntelliSense

## Quick Start

### Simple Integration

Just include the script - no HTML div needed!

```html
<script src="dist/chatbot-widget.iife.js"></script>
<script>
ChatbotWidget.push({
  action: 'init',
  args: {
    namespace: 'green-namespace',
    proxyConfig: {
      proxyUrl: 'http://localhost:3001'
    }
  }
});
</script>
```

### Advanced Configuration

```html
<script src="dist/chatbot-widget.iife.js"></script>
<script>
ChatbotWidget.push({
  action: 'init',
  args: {
    namespace: 'blue-namespace',        // Theme selection
    layoutMode: 'window',               // Display mode
    visibility: 'minimized',            // Initial state
    topic: 'Product Support',           // Context
    proxyConfig: {
      proxyUrl: 'http://localhost:3001',
      debug: true
    },
    context: {
      contentLocale: 'en-US',
      userLocale: 'en-US',
      variables: {
        name: 'John Doe',
        event: 'product_inquiry'
      }
    }
  }
});
</script>
```

## API Reference

### Basic Actions

```javascript
// Open the chat widget
ChatbotWidget.push({ action: 'open' });

// Close the chat widget
ChatbotWidget.push({ action: 'close' });

// Initialize DirectLine connection manually
ChatbotWidget.push({ action: 'initializeConnection' });

// Send a message programmatically
ChatbotWidget.push({ 
  action: 'sendMessage', 
  args: { text: 'Hello!' } 
});
```

### Advanced Configuration

```javascript
// Set user information
ChatbotWidget.push({
  action: 'setUserInfo',
  args: {
    userInfo: { name: 'John Doe', email: '<EMAIL>' }
  }
});

// Change topic context
ChatbotWidget.push({
  action: 'setTopic',
  args: { topic: 'Technical Support' }
});

// Clear conversation
ChatbotWidget.push({ action: 'clearMessages' });
```

## Theme System

Choose from 5 professional themes:

- `green-namespace` - Nature-inspired green theme
- `blue-namespace` - Professional blue theme  
- `purple-namespace` - Creative purple theme
- `light-namespace` - Clean minimal theme
- `dark-namespace` - High contrast dark theme

## Connection Management

**Lazy Loading**: DirectLine connection only initiates when:
1. User clicks the chat button, OR
2. Manual API call: `ChatbotWidget.push({ action: 'initializeConnection' })`

This prevents unnecessary connections and improves page load performance.

## Development

### Prerequisites

- Node.js 18+
- npm

### Setup

```bash
npm install
```

### Development Mode

```bash
npm run dev
```

### Build

```bash
npm run build
```

This creates `dist/chatbot-widget.iife.js` (~260KB, ~80KB gzipped).

### Testing

Open `test.html` in a browser to test all themes and features.

## Architecture

### DirectLine Proxy Integration

The widget uses a proxy server architecture for secure Bot Framework integration:

```
Browser Widget ←→ Proxy Server ←→ DirectLine API ←→ Copilot Studio
```

**Key Benefits**:
- Secure token management
- Real-time WebSocket streaming
- Bot Framework compatibility
- Session persistence

### File Structure

```
src/
├── api/                    # API communication layer
├── components/             # React UI components
│   ├── Avatar.tsx         # User/bot avatars
│   ├── ChatbotButton.tsx  # Floating button
│   ├── ChatbotWidget.tsx  # Main widget container
│   ├── SuggestedActions.tsx # Interactive action buttons
│   └── MessageTimestamp.tsx # Message timing
├── contexts/              # React contexts
│   ├── ProxyBotFrameworkProvider.tsx # DirectLine integration
│   └── ThemeContext.tsx   # Theme management
├── hooks/                 # Custom React hooks
│   ├── useMessageHandler.ts # WebSocket message handling
│   └── useChatbot.ts      # Main chatbot hook
├── services/              # Business logic
│   └── sessionManager.ts  # Session persistence
├── theme/                 # Design system
│   ├── themes.ts          # 5 theme definitions
│   ├── tokens.ts          # Design tokens
│   └── themeCSS.ts        # CSS generation
├── types/                 # TypeScript definitions
└── main.tsx              # Entry point
```

## Proxy Server

Required for DirectLine integration:

```bash
cd directline-proxy-server
npm install
npm start  # Runs on http://localhost:3001
```

See `directline-proxy-server/README.md` for setup details.