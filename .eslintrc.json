{"extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks", "prettier"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2022": true}, "settings": {"react": {"version": "detect"}}, "rules": {"prettier/prettier": "error", "react/react-in-jsx-scope": "off"}}