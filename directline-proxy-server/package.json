{"name": "directline-proxy-server", "version": "1.0.0", "description": "Secure proxy server for Bot Framework DirectLine API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["bot-framework", "directline", "proxy", "security"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0", "socket.io": "^4.8.1", "uuid": "^11.0.3", "winston": "^3.17.0", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.1.9"}}