const fetch = require('node-fetch');
const WebSocket = require('ws');
const logger = require('../utils/logger');

class DirectLineService {
  constructor() {
    this.baseUrl = 'https://directline.botframework.com/v3/directline';
    this.secret = process.env.DIRECTLINE_SECRET;
    
    if (!this.secret) {
      throw new Error('DIRECTLINE_SECRET environment variable is required');
    }
  }

  /**
   * Generate a DirectLine token from the secret
   * @param {string} userId - Optional user ID for the conversation
   * @returns {Promise<Object>} Token response with token and expiration
   */
  async generateToken(userId = null) {
    try {
      logger.info('Generating DirectLine token', { userId });
      
      const payload = {};
      if (userId) {
        payload.User = {
          Id: userId,
          Name: `User_${userId}`
        };
      }
      
      const response = await fetch(`${this.baseUrl}/tokens/generate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.secret}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('DirectLine token generation failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Token generation failed: ${response.status} ${response.statusText}`);
      }

      const tokenData = await response.json();
      
      logger.info('DirectLine token generated successfully', {
        userId,
        expiresIn: tokenData.expires_in
      });

      return {
        token: tokenData.token,
        expires_in: tokenData.expires_in,
        issued_at: Date.now()
      };
    } catch (error) {
      logger.error('DirectLine token generation error', {
        error: error.message,
        userId
      });
      throw error;
    }
  }

  /**
   * Refresh an existing DirectLine token
   * @param {string} token - The existing token to refresh
   * @returns {Promise<Object>} New token response
   */
  async refreshToken(token) {
    try {
      logger.info('Refreshing DirectLine token');
      
      const response = await fetch(`${this.baseUrl}/tokens/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('DirectLine token refresh failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Token refresh failed: ${response.status} ${response.statusText}`);
      }

      const tokenData = await response.json();
      
      logger.info('DirectLine token refreshed successfully', {
        expiresIn: tokenData.expires_in
      });

      return {
        token: tokenData.token,
        expires_in: tokenData.expires_in,
        issued_at: Date.now()
      };
    } catch (error) {
      logger.error('DirectLine token refresh error', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Create a new conversation
   * @param {string} token - DirectLine token
   * @returns {Promise<Object>} Conversation data
   */
  async createConversation(token) {
    try {
      logger.info('Creating DirectLine conversation');
      
      const response = await fetch(`${this.baseUrl}/conversations`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('DirectLine conversation creation failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Conversation creation failed: ${response.status} ${response.statusText}`);
      }

      const conversationData = await response.json();
      
      logger.info('DirectLine conversation created successfully', {
        conversationId: conversationData.conversationId
      });

      return conversationData;
    } catch (error) {
      logger.error('DirectLine conversation creation error', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Send an activity to a conversation
   * @param {string} token - DirectLine token
   * @param {string} conversationId - Conversation ID
   * @param {Object} activity - Activity to send
   * @returns {Promise<Object>} Activity response
   */
  async sendActivity(token, conversationId, activity) {
    try {
      logger.info('Sending activity to DirectLine', {
        conversationId,
        activityType: activity.type,
        text: activity.text
      });
      
      const response = await fetch(`${this.baseUrl}/conversations/${conversationId}/activities`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activity)
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error('DirectLine activity send failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText,
          conversationId
        });
        throw new Error(`Activity send failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      logger.info('DirectLine activity sent successfully', {
        conversationId,
        activityId: result.id
      });

      return result;
    } catch (error) {
      logger.error('DirectLine activity send error', {
        error: error.message,
        conversationId
      });
      throw error;
    }
  }


  /**
   * Connect to DirectLine WebSocket stream for real-time messages
   * @param {string} streamUrl - WebSocket stream URL from conversation creation
   * @param {function} onMessage - Callback for received activities
   * @param {function} onError - Callback for connection errors
   * @param {function} onClose - Callback for connection close
   * @returns {WebSocket} WebSocket connection
   */
  connectWebSocketStream(streamUrl, onMessage, onError, onClose) {
    try {
      logger.info('Connecting to DirectLine WebSocket stream', { streamUrl });
      
      const ws = new WebSocket(streamUrl);
      
      ws.on('open', () => {
        logger.info('DirectLine WebSocket connected successfully');
      });
      
      ws.on('message', (data) => {
        try {
          const activitySet = JSON.parse(data.toString());
          logger.debug('DirectLine WebSocket message received', {
            activityCount: activitySet.activities ? activitySet.activities.length : 0,
            watermark: activitySet.watermark
          });
          
          if (onMessage) {
            onMessage(activitySet);
          }
        } catch (error) {
          logger.error('Error parsing WebSocket message', { error: error.message, data: data.toString() });
        }
      });
      
      ws.on('error', (error) => {
        logger.error('DirectLine WebSocket error', { error: error.message });
        if (onError) {
          onError(error);
        }
      });
      
      ws.on('close', (code, reason) => {
        logger.info('DirectLine WebSocket closed', { code, reason: reason.toString() });
        if (onClose) {
          onClose(code, reason);
        }
      });
      
      return ws;
    } catch (error) {
      logger.error('DirectLine WebSocket connection error', { error: error.message });
      throw error;
    }
  }
}

module.exports = DirectLineService;