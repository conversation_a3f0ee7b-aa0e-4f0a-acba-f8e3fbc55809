const logger = require('../utils/logger');

/**
 * Token management service for DirectLine tokens
 */
class TokenService {
  constructor() {
    // In-memory token cache (in production, use Redis or similar)
    this.tokenCache = new Map();
    this.TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // Refresh 5 minutes before expiration
  }

  /**
   * Store token in cache
   * @param {string} key - Cache key (usually user ID or session ID)
   * @param {Object} tokenData - Token data with token, expires_in, issued_at
   */
  storeToken(key, tokenData) {
    const expiresAt = tokenData.issued_at + (tokenData.expires_in * 1000);
    
    this.tokenCache.set(key, {
      ...tokenData,
      expires_at: expiresAt
    });
    
    logger.debug('Token stored in cache', {
      key,
      expiresAt: new Date(expiresAt).toISOString()
    });
  }

  /**
   * Get token from cache
   * @param {string} key - Cache key
   * @returns {Object|null} Token data or null if not found
   */
  getToken(key) {
    const tokenData = this.tokenCache.get(key);
    
    if (!tokenData) {
      logger.debug('Token not found in cache', { key });
      return null;
    }
    
    // Check if token is still valid
    if (this.isTokenExpired(tokenData)) {
      logger.info('Removing expired token from cache', { key });
      this.tokenCache.delete(key);
      return null;
    }
    
    return tokenData;
  }

  /**
   * Check if token is expired
   * @param {Object} tokenData - Token data with expires_at
   * @returns {boolean} True if token is expired
   */
  isTokenExpired(tokenData) {
    return Date.now() >= tokenData.expires_at;
  }

  /**
   * Check if token needs refresh
   * @param {Object} tokenData - Token data with expires_at
   * @returns {boolean} True if token should be refreshed
   */
  shouldRefreshToken(tokenData) {
    const timeUntilExpiry = tokenData.expires_at - Date.now();
    return timeUntilExpiry <= this.TOKEN_REFRESH_THRESHOLD;
  }

  /**
   * Remove token from cache
   * @param {string} key - Cache key
   */
  removeToken(key) {
    const removed = this.tokenCache.delete(key);
    if (removed) {
      logger.debug('Token removed from cache', { key });
    }
    return removed;
  }

  /**
   * Clear expired tokens from cache (cleanup task)
   */
  clearExpiredTokens() {
    const now = Date.now();
    let clearedCount = 0;
    
    for (const [key, tokenData] of this.tokenCache.entries()) {
      if (now >= tokenData.expires_at) {
        this.tokenCache.delete(key);
        clearedCount++;
      }
    }
    
    if (clearedCount > 0) {
      logger.info('Cleared expired tokens from cache', { count: clearedCount });
    }
    
    return clearedCount;
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    const now = Date.now();
    let activeTokens = 0;
    let expiredTokens = 0;
    
    for (const tokenData of this.tokenCache.values()) {
      if (now >= tokenData.expires_at) {
        expiredTokens++;
      } else {
        activeTokens++;
      }
    }
    
    return {
      totalTokens: this.tokenCache.size,
      activeTokens,
      expiredTokens
    };
  }

  /**
   * Generate a cache key for a user session
   * @param {string} userId - User ID
   * @param {string} sessionId - Session ID (optional)
   * @returns {string} Cache key
   */
  generateCacheKey(userId, sessionId = null) {
    return sessionId ? `${userId}:${sessionId}` : userId;
  }
}

module.exports = TokenService;