require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
const logger = require('./utils/logger');
const { rateLimiter, securityHeaders, requestLogger, errorHandler } = require('./middleware/security');
const { validateApiKey } = require('./middleware/auth');
const AuthController = require('./controllers/authController');
const ConversationController = require('./controllers/conversationController');

// Initialize Express app and HTTP server
const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 3001;

// Initialize Socket.IO with CORS
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? ['https://yourdomain.com'] 
      : ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5500'],
    credentials: true,
    methods: ['GET', 'POST']
  }
});

// Initialize controllers with Socket.IO
const authController = new AuthController();
const conversationController = new ConversationController(io);

// Trust proxy (for proper IP detection behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(securityHeaders);
app.use(rateLimiter);

// CORS configuration
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] // Replace with your actual domains
    : ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5500', 'null'], // Allow local development
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Request logging
app.use(requestLogger);

// Health check endpoint (no auth required)
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV
  });
});

// API documentation endpoint
app.get('/', (req, res) => {
  res.json({
    name: 'DirectLine Proxy Server',
    version: '1.0.0',
    description: 'Secure proxy server for Bot Framework DirectLine API',
    endpoints: {
      health: 'GET /health',
      auth: {
        generateToken: 'POST /api/auth/token',
        refreshToken: 'POST /api/auth/refresh',
        revokeToken: 'DELETE /api/auth/token',
        stats: 'GET /api/auth/stats'
      },
      conversations: {
        create: 'POST /api/conversations',
        sendActivity: 'POST /api/conversations/:id/activities',
        getInfo: 'GET /api/conversations/:id',
        delete: 'DELETE /api/conversations/:id',
        stats: 'GET /api/conversations/stats'
      }
    },
    timestamp: new Date().toISOString()
  });
});

// Apply authentication middleware to protected routes
app.use('/api', validateApiKey);

// Authentication routes
app.post('/api/auth/token', (req, res) => authController.generateToken(req, res));
app.post('/api/auth/refresh', (req, res) => authController.refreshToken(req, res));
app.delete('/api/auth/token', (req, res) => authController.revokeToken(req, res));
app.get('/api/auth/stats', (req, res) => authController.getStats(req, res));

// Conversation routes
app.post('/api/conversations', (req, res) => conversationController.createConversation(req, res));
app.post('/api/conversations/:conversationId/activities', (req, res) => conversationController.sendActivity(req, res));
app.get('/api/conversations/:conversationId', (req, res) => conversationController.getConversationInfo(req, res));
app.get('/api/conversations/:conversationId/messages', (req, res) => conversationController.getMessageHistory(req, res));
app.delete('/api/conversations/:conversationId', (req, res) => conversationController.deleteConversation(req, res));
app.get('/api/conversations/stats', (req, res) => conversationController.getStats(req, res));

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// WebSocket connection handling
io.on('connection', (socket) => {
  logger.info('Client connected via WebSocket', {
    socketId: socket.id,
    ip: socket.handshake.address
  });

  socket.on('join-conversation', (data) => {
    const { conversationId, userId } = data;
    socket.join(`conversation:${conversationId}`);
    logger.info('Client joined conversation room', {
      socketId: socket.id,
      conversationId,
      userId
    });
  });

  socket.on('disconnect', () => {
    logger.info('Client disconnected', { socketId: socket.id });
  });
});

// Start server
server.listen(PORT, () => {
  logger.info(`🚀 DirectLine Proxy Server running on port ${PORT}`, {
    port: PORT,
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
  
  logger.info('📊 Available endpoints:', {
    health: `http://localhost:${PORT}/health`,
    docs: `http://localhost:${PORT}/`,
    auth: `http://localhost:${PORT}/api/auth/token`,
    conversations: `http://localhost:${PORT}/api/conversations`,
    websocket: `ws://localhost:${PORT}`
  });
});

// Handle graceful shutdown
const gracefulShutdown = (signal) => {
  logger.info(`📴 ${signal} received, shutting down gracefully...`);
  
  server.close(() => {
    logger.info('✅ HTTP server closed');
    
    // Close Socket.IO connections
    io.close(() => {
      logger.info('✅ WebSocket server closed');
      process.exit(0);
    });
  });
  
  // Force close after 30 seconds
  setTimeout(() => {
    logger.error('❌ Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', {
    promise: promise,
    reason: reason,
    stack: reason?.stack
  });
  
  // Close server gracefully
  gracefulShutdown('UNHANDLED_REJECTION');
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', {
    error: error.message,
    stack: error.stack
  });
  
  // Close server gracefully
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

module.exports = app;