const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

// Simple token validation (for future expansion)
const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  const authHeader = req.headers.authorization;
  
  // For now, we'll allow requests without authentication for development
  // This middleware is prepared for future authentication implementation
  
  if (process.env.NODE_ENV === 'production' && !apiKey && !authHeader) {
    logger.warn('Unauthorized access attempt', {
      ip: req.ip,
      path: req.path,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(401).json({
      error: 'Authentication required',
      message: 'API key or authorization token required'
    });
  }
  
  // Add user context to request (for future use)
  req.user = {
    id: apiKey || 'anonymous',
    isAuthenticated: !!(apiKey || authHeader)
  };
  
  next();
};

// JWT token validation (for future use when implementing user authentication)
const validateJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      error: 'Invalid authorization header',
      message: 'Bearer token required'
    });
  }
  
  const token = authHeader.substring(7);
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    logger.warn('Invalid JWT token', {
      error: error.message,
      ip: req.ip,
      path: req.path
    });
    
    res.status(401).json({
      error: 'Invalid token',
      message: 'Token validation failed'
    });
  }
};

// Generate JWT token (for future user authentication)
const generateToken = (payload, expiresIn = '24h') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

module.exports = {
  validateApiKey,
  validateJWT,
  generateToken
};