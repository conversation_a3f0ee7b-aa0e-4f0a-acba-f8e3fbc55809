const DirectLineService = require('../services/directLineService');
const TokenService = require('../services/tokenService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class AuthController {
  constructor() {
    this.directLineService = new DirectLineService();
    this.tokenService = new TokenService();
  }

  /**
   * Generate DirectLine token endpoint
   * POST /api/auth/token
   */
  async generateToken(req, res) {
    try {
      const { userId, sessionId } = req.body;
      const userIdentifier = userId || uuidv4();
      const cacheKey = this.tokenService.generateCacheKey(userIdentifier, sessionId);

      logger.info('Token generation requested', {
        userId: userIdentifier,
        sessionId,
        ip: req.ip
      });

      // Check if we have a valid cached token
      const cachedToken = this.tokenService.getToken(cacheKey);
      
      if (cachedToken && !this.tokenService.shouldRefreshToken(cachedToken)) {
        logger.info('Returning cached token', {
          userId: userIdentifier,
          expiresAt: new Date(cachedToken.expires_at).toISOString()
        });

        return res.json({
          token: cachedToken.token,
          expires_in: Math.floor((cachedToken.expires_at - Date.now()) / 1000),
          user_id: userIdentifier,
          cached: true
        });
      }

      // Generate new token or refresh existing one
      let tokenData;
      
      if (cachedToken && this.tokenService.shouldRefreshToken(cachedToken)) {
        logger.info('Refreshing existing token', { userId: userIdentifier });
        try {
          tokenData = await this.directLineService.refreshToken(cachedToken.token);
        } catch (refreshError) {
          logger.warn('Token refresh failed, generating new token', {
            userId: userIdentifier,
            error: refreshError.message
          });
          tokenData = await this.directLineService.generateToken(userIdentifier);
        }
      } else {
        logger.info('Generating new token', { userId: userIdentifier });
        tokenData = await this.directLineService.generateToken(userIdentifier);
      }

      // Store token in cache
      this.tokenService.storeToken(cacheKey, tokenData);

      // Return token to client
      res.json({
        token: tokenData.token,
        expires_in: tokenData.expires_in,
        user_id: userIdentifier,
        issued_at: new Date(tokenData.issued_at).toISOString()
      });

      logger.info('Token generated successfully', {
        userId: userIdentifier,
        expiresIn: tokenData.expires_in
      });

    } catch (error) {
      logger.error('Token generation failed', {
        error: error.message,
        stack: error.stack,
        userId: req.body?.userId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Token generation failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Refresh DirectLine token endpoint
   * POST /api/auth/refresh
   */
  async refreshToken(req, res) {
    try {
      const { token, userId, sessionId } = req.body;

      if (!token) {
        return res.status(400).json({
          error: 'Missing required parameter',
          message: 'Token is required'
        });
      }

      logger.info('Token refresh requested', {
        userId,
        sessionId,
        ip: req.ip
      });

      const refreshedToken = await this.directLineService.refreshToken(token);
      
      // Update cache if userId is provided
      if (userId) {
        const cacheKey = this.tokenService.generateCacheKey(userId, sessionId);
        this.tokenService.storeToken(cacheKey, refreshedToken);
      }

      res.json({
        token: refreshedToken.token,
        expires_in: refreshedToken.expires_in,
        issued_at: new Date(refreshedToken.issued_at).toISOString()
      });

      logger.info('Token refreshed successfully', {
        userId,
        expiresIn: refreshedToken.expires_in
      });

    } catch (error) {
      logger.error('Token refresh failed', {
        error: error.message,
        userId: req.body?.userId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Token refresh failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Revoke token endpoint (cleanup)
   * DELETE /api/auth/token
   */
  async revokeToken(req, res) {
    try {
      const { userId, sessionId } = req.body;

      if (!userId) {
        return res.status(400).json({
          error: 'Missing required parameter',
          message: 'userId is required'
        });
      }

      const cacheKey = this.tokenService.generateCacheKey(userId, sessionId);
      const removed = this.tokenService.removeToken(cacheKey);

      logger.info('Token revocation requested', {
        userId,
        sessionId,
        removed,
        ip: req.ip
      });

      res.json({
        success: true,
        message: removed ? 'Token revoked successfully' : 'Token not found in cache'
      });

    } catch (error) {
      logger.error('Token revocation failed', {
        error: error.message,
        userId: req.body?.userId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Token revocation failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Get token cache statistics (for monitoring)
   * GET /api/auth/stats
   */
  async getStats(req, res) {
    try {
      const stats = this.tokenService.getCacheStats();
      
      // Clean up expired tokens
      const cleaned = this.tokenService.clearExpiredTokens();
      
      res.json({
        cache: stats,
        cleaned_expired_tokens: cleaned,
        timestamp: new Date().toISOString()
      });

      logger.debug('Token cache stats requested', stats);

    } catch (error) {
      logger.error('Failed to get token stats', {
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Failed to get statistics',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
}

module.exports = AuthController;