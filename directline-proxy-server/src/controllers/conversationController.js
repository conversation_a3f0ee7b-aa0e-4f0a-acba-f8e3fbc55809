const DirectLineService = require('../services/directLineService');
const TokenService = require('../services/tokenService');
const logger = require('../utils/logger');

class ConversationController {
  constructor(io = null) {
    this.directLineService = new DirectLineService();
    this.tokenService = new TokenService();
    this.io = io;
    // In-memory conversation cache (in production, use Redis or database)
    this.conversationCache = new Map();
    // Track active WebSocket connections for cleanup
    this.webSocketConnections = new Map();
  }

  /**
   * Create a new conversation
   * POST /api/conversations
   */
  async createConversation(req, res) {
    try {
      const { token, userId, sessionId } = req.body;

      if (!token) {
        return res.status(400).json({
          error: 'Missing required parameter',
          message: 'Token is required'
        });
      }

      logger.info('Conversation creation requested', {
        userId,
        sessionId,
        ip: req.ip
      });

      const conversation = await this.directLineService.createConversation(token);
      
      // Cache conversation data
      const cacheKey = `conv_${conversation.conversationId}`;
      this.conversationCache.set(cacheKey, {
        ...conversation,
        userId,
        sessionId,
        created_at: Date.now(),
        last_activity: Date.now(),
        watermark: ''
      });

      // Start DirectLine WebSocket streaming for this conversation
      this.startDirectLineStreaming(conversation.conversationId, conversation.streamUrl, userId);

      res.json({
        conversationId: conversation.conversationId,
        token: conversation.token || token,
        expires_in: conversation.expires_in,
        userId,
        created_at: new Date().toISOString()
      });

      logger.info('Conversation created successfully', {
        conversationId: conversation.conversationId,
        userId
      });

    } catch (error) {
      logger.error('Conversation creation failed', {
        error: error.message,
        stack: error.stack,
        userId: req.body?.userId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Conversation creation failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Send activity to conversation
   * POST /api/conversations/:conversationId/activities
   */
  async sendActivity(req, res) {
    try {
      const { conversationId } = req.params;
      const { token, activity } = req.body;

      if (!token || !activity) {
        return res.status(400).json({
          error: 'Missing required parameters',
          message: 'Token and activity are required'
        });
      }

      // Validate activity structure
      if (!activity.type || !activity.from) {
        return res.status(400).json({
          error: 'Invalid activity structure',
          message: 'Activity must have type and from properties'
        });
      }

      logger.info('Activity send requested', {
        conversationId,
        activityType: activity.type,
        text: activity.text,
        fromId: activity.from.id,
        ip: req.ip
      });

      const result = await this.directLineService.sendActivity(token, conversationId, activity);
      
      // Update conversation last activity time
      const cacheKey = `conv_${conversationId}`;
      const cachedConv = this.conversationCache.get(cacheKey);
      if (cachedConv) {
        cachedConv.last_activity = Date.now();
      }

      res.json({
        id: result.id,
        conversationId,
        timestamp: new Date().toISOString()
      });

      logger.info('Activity sent successfully', {
        conversationId,
        activityId: result.id,
        activityType: activity.type
      });

    } catch (error) {
      logger.error('Activity send failed', {
        error: error.message,
        conversationId: req.params.conversationId,
        activityType: req.body?.activity?.type,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Activity send failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Get message history for a conversation
   * GET /api/conversations/:conversationId/messages
   */
  async getMessageHistory(req, res) {
    try {
      const { conversationId } = req.params;
      const { token } = req.query; // Accept token as query parameter for existing sessions
      const cacheKey = `conv_${conversationId}`;
      const cachedConv = this.conversationCache.get(cacheKey);

      // For existing sessions, we might not have cached conversation data
      // but we can still fetch message history if token is provided
      let authToken = cachedConv?.token || cachedConv?.conversationToken || token;
      let userId = cachedConv?.userId || 'user'; // Default userId if not cached

      if (!authToken) {
        return res.status(400).json({
          error: 'Authentication token required',
          message: 'Conversation not found in cache and no token provided. Include token as query parameter.'
        });
      }

      logger.info('Message history requested', {
        conversationId,
        userId,
        fromCache: !!cachedConv,
        ip: req.ip
      });

      try {
        // Fetch activities from DirectLine API
        const response = await fetch(`https://directline.botframework.com/v3/directline/conversations/${conversationId}/activities`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`DirectLine API error: ${response.status}`);
        }

        const activitySet = await response.json();
        console.log('DirectLine message history response:', activitySet);

        // Filter and convert activities to our message format
        const messages = [];
        if (activitySet.activities) {
          for (const activity of activitySet.activities) {
            // Include both user and bot messages
            const isUserMessage = activity.from.id === userId || 
                                 activity.from.id === 'User' || 
                                 activity.from.id === 'user' ||
                                 activity.from.id.startsWith('user_');
            
            if (activity.type === 'message' && activity.text) {
              // Map DirectLine suggestedActions format to frontend format (only for bot messages)
              let mappedSuggestedActions = null;
              if (!isUserMessage && activity.suggestedActions && activity.suggestedActions.actions) {
                mappedSuggestedActions = {
                  actions: activity.suggestedActions.actions.map(action => ({
                    type: action.type,
                    title: action.title,
                    text: action.value || action.title,
                    value: action.value
                  }))
                };
              }

              const message = {
                id: activity.id,
                text: activity.text,
                isUser: isUserMessage,
                timestamp: new Date(activity.timestamp || Date.now()),
                from: activity.from,
                ...(mappedSuggestedActions && { suggestedActions: mappedSuggestedActions })
              };
              
              messages.push(message);
            }
          }
        }

        res.json({
          conversationId,
          messages,
          messageCount: messages.length
        });

        logger.info('Message history retrieved successfully', {
          conversationId,
          messageCount: messages.length
        });

      } catch (directLineError) {
        logger.error('DirectLine message history fetch failed', {
          conversationId,
          error: directLineError.message
        });

        // Return empty messages if DirectLine fails (conversation might be expired)
        res.json({
          conversationId,
          messages: [],
          messageCount: 0,
          warning: 'Could not fetch message history - conversation may have expired'
        });
      }

    } catch (error) {
      logger.error('Message history request failed', {
        error: error.message,
        conversationId: req.params.conversationId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Message history request failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Get conversation info
   * GET /api/conversations/:conversationId
   */
  async getConversationInfo(req, res) {
    try {
      const { conversationId } = req.params;
      const cacheKey = `conv_${conversationId}`;
      const cachedConv = this.conversationCache.get(cacheKey);

      if (!cachedConv) {
        return res.status(404).json({
          error: 'Conversation not found',
          message: 'Conversation not found in cache'
        });
      }

      res.json({
        conversationId: cachedConv.conversationId,
        userId: cachedConv.userId,
        sessionId: cachedConv.sessionId,
        created_at: new Date(cachedConv.created_at).toISOString(),
        last_activity: new Date(cachedConv.last_activity).toISOString()
      });

      logger.debug('Conversation info retrieved', {
        conversationId,
        userId: cachedConv.userId
      });

    } catch (error) {
      logger.error('Get conversation info failed', {
        error: error.message,
        conversationId: req.params.conversationId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Get conversation info failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Delete conversation (cleanup)
   * DELETE /api/conversations/:conversationId
   */
  async deleteConversation(req, res) {
    try {
      const { conversationId } = req.params;
      
      // Stop WebSocket streaming for this conversation
      this.stopDirectLineStreaming(conversationId);
      
      const cacheKey = `conv_${conversationId}`;
      const removed = this.conversationCache.delete(cacheKey);

      logger.info('Conversation deletion requested', {
        conversationId,
        removed,
        ip: req.ip
      });

      res.json({
        success: true,
        message: removed ? 'Conversation deleted successfully' : 'Conversation not found in cache'
      });

    } catch (error) {
      logger.error('Conversation deletion failed', {
        error: error.message,
        conversationId: req.params.conversationId,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Conversation deletion failed',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Get conversation statistics (for monitoring)
   * GET /api/conversations/stats
   */
  async getStats(req, res) {
    try {
      const now = Date.now();
      const conversations = Array.from(this.conversationCache.values());
      
      const stats = {
        total_conversations: conversations.length,
        active_conversations: conversations.filter(c => now - c.last_activity < 30 * 60 * 1000).length, // Active in last 30 minutes
        oldest_conversation: conversations.length > 0 ? Math.min(...conversations.map(c => c.created_at)) : null,
        newest_conversation: conversations.length > 0 ? Math.max(...conversations.map(c => c.created_at)) : null
      };

      if (stats.oldest_conversation) {
        stats.oldest_conversation = new Date(stats.oldest_conversation).toISOString();
      }
      if (stats.newest_conversation) {
        stats.newest_conversation = new Date(stats.newest_conversation).toISOString();
      }

      res.json({
        ...stats,
        timestamp: new Date().toISOString()
      });

      logger.debug('Conversation stats requested', stats);

    } catch (error) {
      logger.error('Failed to get conversation stats', {
        error: error.message,
        ip: req.ip
      });

      res.status(500).json({
        error: 'Failed to get statistics',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  /**
   * Start DirectLine WebSocket streaming for a conversation
   */
  startDirectLineStreaming(conversationId, streamUrl, userId) {
    const cacheKey = `conv_${conversationId}`;
    const cachedConv = this.conversationCache.get(cacheKey);
    
    if (!cachedConv) {
      logger.error('Cannot start streaming for unknown conversation', { conversationId });
      return;
    }

    logger.info('Starting DirectLine WebSocket streaming for conversation', { conversationId, streamUrl });

    const onMessage = (activitySet) => {
      console.log('WebSocket message activity set:', activitySet);
      try {
        if (activitySet.activities && activitySet.activities.length > 0) {
          // Filter for bot messages only (not user echoes)
          const botMessages = activitySet.activities.filter(activity => {
            const isUserMessage = activity.from.id === userId || 
                                 activity.from.id === cachedConv.userId || 
                                 activity.from.id === 'User' || 
                                 activity.from.id === 'user' ||
                                 activity.from.id.startsWith('user_');
            
            const isBotMessage = activity.type === 'message' && 
                               !isUserMessage && 
                               activity.text;
            
            logger.debug('WebSocket message filtering', {
              conversationId,
              activityId: activity.id,
              fromId: activity.from.id,
              userId,
              cachedUserId: cachedConv.userId,
              isUserMessage,
              isBotMessage,
              text: activity.text
            });
            
            return isBotMessage;
          });

          if (botMessages.length > 0) {
            logger.info('Bot messages received from DirectLine WebSocket', {
              conversationId,
              messageCount: botMessages.length,
              messages: botMessages.map(m => ({ text: m.text, from: m.from.id }))
            });

            // Emit each bot message via our WebSocket
            botMessages.forEach(activity => {
              // Map DirectLine suggestedActions format to frontend format
              let mappedSuggestedActions = null;
              if (activity.suggestedActions && activity.suggestedActions.actions) {
                mappedSuggestedActions = {
                  actions: activity.suggestedActions.actions.map(action => ({
                    type: action.type,
                    title: action.title,
                    text: action.value || action.title, // Map DirectLine 'value' to frontend 'text'
                    value: action.value
                  }))
                };
                console.log('🔧 Mapped suggestedActions for frontend:', mappedSuggestedActions);
              }

              const message = {
                id: activity.id,
                text: activity.text,
                isUser: false, // These are confirmed bot messages after filtering
                timestamp: new Date(activity.timestamp || Date.now()),
                from: activity.from,
                ...(mappedSuggestedActions && { suggestedActions: mappedSuggestedActions })
              };

              if (this.io) {
                this.io.to(`conversation:${conversationId}`).emit('bot-message', { message, activity });
                logger.debug('Bot message emitted via our WebSocket', {
                  conversationId,
                  messageId: message.id,
                  text: message.text,
                  fromId: activity.from.id,
                  suggestedActions: activity.suggestedActions
                });
              }
            });
          }

          // Update watermark
          if (activitySet.watermark) {
            cachedConv.watermark = activitySet.watermark;
          }
        }

        // Update last activity timestamp
        cachedConv.last_activity = Date.now();

      } catch (error) {
        logger.error('DirectLine WebSocket message processing error', {
          conversationId,
          error: error.message
        });
      }
    };

    const onError = (error) => {
      logger.error('DirectLine WebSocket error', {
        conversationId,
        error: error.message
      });
    };

    const onClose = (code, reason) => {
      logger.info('DirectLine WebSocket closed', {
        conversationId,
        code,
        reason: reason.toString()
      });
      
      // Clean up connection reference
      this.webSocketConnections.delete(conversationId);
    };

    // Connect to DirectLine WebSocket stream
    const ws = this.directLineService.connectWebSocketStream(streamUrl, onMessage, onError, onClose);
    
    // Store connection for cleanup
    this.webSocketConnections.set(conversationId, ws);

    logger.info('DirectLine WebSocket streaming started', { 
      conversationId
    });
  }

  /**
   * Stop DirectLine WebSocket streaming for a conversation
   */
  stopDirectLineStreaming(conversationId) {
    const ws = this.webSocketConnections.get(conversationId);
    if (ws) {
      ws.close();
      this.webSocketConnections.delete(conversationId);
      logger.info('DirectLine WebSocket streaming stopped', { conversationId });
    }
  }
}

module.exports = ConversationController;