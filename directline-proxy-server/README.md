# DirectLine Proxy Server

A secure proxy server for Microsoft Bot Framework DirectLine API that provides token-based authentication and protects DirectLine secrets from client-side exposure.

## 🚀 Features

- **Secure Token Management**: Converts DirectLine secrets to time-limited tokens
- **Token Caching**: Intelligent token caching with automatic refresh
- **Rate Limiting**: Protects against abuse with configurable rate limits
- **Security Headers**: Comprehensive security headers via Helmet.js
- **Request Logging**: Detailed logging for monitoring and debugging
- **Conversation Management**: Full DirectLine conversation lifecycle management
- **Health Monitoring**: Health check and statistics endpoints
- **CORS Support**: Configurable CORS for different environments

## 📁 Project Structure

```
directline-proxy-server/
├── src/
│   ├── controllers/
│   │   ├── authController.js      # Token generation and management
│   │   └── conversationController.js  # Conversation and message handling
│   ├── middleware/
│   │   ├── auth.js               # Authentication middleware
│   │   └── security.js           # Security and rate limiting
│   ├── services/
│   │   ├── directLineService.js  # DirectLine API client
│   │   └── tokenService.js       # Token caching and management
│   ├── utils/
│   │   └── logger.js             # Winston logging configuration
│   └── app.js                    # Express application setup
├── .env.example                  # Environment variables template
├── .env                         # Local environment variables
├── .gitignore                   # Git ignore rules
├── package.json                 # Dependencies and scripts
└── README.md                    # This file
```

## 🛠️ Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your DirectLine secret and configuration
   ```

3. **Start the server:**
   ```bash
   # Development mode with auto-reload
   npm run dev
   
   # Production mode
   npm start
   ```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DIRECTLINE_SECRET` | Bot Framework DirectLine secret | - | ✅ |
| `PORT` | Server port | 3001 | ❌ |
| `NODE_ENV` | Environment (development/production) | development | ❌ |
| `JWT_SECRET` | JWT secret for token validation | - | ✅ |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window in milliseconds | 900000 | ❌ |
| `RATE_LIMIT_MAX_REQUESTS` | Max requests per window | 100 | ❌ |
| `LOG_LEVEL` | Logging level | info | ❌ |

### CORS Configuration

The server is configured to allow requests from:
- **Development**: `localhost:5173`, `localhost:3000`, `127.0.0.1:5500`
- **Production**: Configure your actual domains in `src/app.js`

## 📚 API Documentation

### Health Check
```http
GET /health
```
Returns server health status and basic information.

### Authentication Endpoints

#### Generate Token
```http
POST /api/auth/token
Content-Type: application/json

{
  "userId": "user123",
  "sessionId": "session456"
}
```

**Response:**
```json
{
  "token": "directline_token_here",
  "expires_in": 3600,
  "user_id": "user123",
  "issued_at": "2025-01-27T10:30:00.000Z"
}
```

#### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "token": "existing_token",
  "userId": "user123"
}
```

#### Revoke Token
```http
DELETE /api/auth/token
Content-Type: application/json

{
  "userId": "user123",
  "sessionId": "session456"
}
```

#### Token Statistics
```http
GET /api/auth/stats
```

### Conversation Endpoints

#### Create Conversation
```http
POST /api/conversations
Content-Type: application/json

{
  "token": "directline_token",
  "userId": "user123",
  "sessionId": "session456"
}
```

#### Send Activity
```http
POST /api/conversations/{conversationId}/activities
Content-Type: application/json

{
  "token": "directline_token",
  "activity": {
    "type": "message",
    "from": { "id": "user123", "name": "User" },
    "text": "Hello bot!"
  }
}
```

#### Get Activities
```http
GET /api/conversations/{conversationId}/activities?token={token}&watermark={watermark}
```

#### Get Conversation Info
```http
GET /api/conversations/{conversationId}
```

#### Delete Conversation
```http
DELETE /api/conversations/{conversationId}
```

#### Conversation Statistics
```http
GET /api/conversations/stats
```

## 🔒 Security Features

### Token Management
- **Secret Protection**: DirectLine secrets never leave the server
- **Token Expiration**: All tokens have configurable expiration times
- **Token Refresh**: Automatic token refresh before expiration
- **Token Caching**: Efficient in-memory token caching

### Request Security
- **Rate Limiting**: Configurable rate limits per IP address
- **Security Headers**: Comprehensive security headers via Helmet.js
- **CORS Protection**: Configurable CORS policies
- **Request Validation**: Input validation and sanitization

### Monitoring & Logging
- **Request Logging**: All requests logged with timing and status
- **Error Logging**: Detailed error logging with stack traces
- **Health Monitoring**: Health check and statistics endpoints
- **Graceful Shutdown**: Proper cleanup on server shutdown

## 🧪 Testing

1. **Start the proxy server:**
   ```bash
   npm run dev
   ```

2. **Test health endpoint:**
   ```bash
   curl http://localhost:3001/health
   ```

3. **Test token generation:**
   ```bash
   curl -X POST http://localhost:3001/api/auth/token \
     -H "Content-Type: application/json" \
     -d '{"userId": "testuser"}'
   ```

## 🚀 Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure proper `DIRECTLINE_SECRET`
3. Set secure `JWT_SECRET`
4. Configure production CORS origins
5. Set up proper logging directory permissions

### Security Considerations
1. Use HTTPS in production
2. Configure proper firewall rules
3. Use environment variables for all secrets
4. Regular security updates
5. Monitor logs for suspicious activity

### Scaling
- Use Redis for token/conversation caching in multi-instance deployments
- Configure load balancer health checks to use `/health` endpoint
- Consider using a reverse proxy (nginx) for SSL termination

## 🔧 Integration with Chatbot Widget

The proxy server is designed to work with the chatbot widget by replacing direct DirectLine API calls:

1. **Replace direct DirectLine usage** in the client
2. **Use proxy endpoints** for token generation and message handling
3. **No secrets in client code** - all secrets managed server-side
4. **Improved security** with token-based authentication

## 📊 Monitoring

The server provides several monitoring endpoints:

- `/health` - Server health status
- `/api/auth/stats` - Token cache statistics
- `/api/conversations/stats` - Conversation statistics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details