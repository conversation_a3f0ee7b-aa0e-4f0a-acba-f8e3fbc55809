# Chatbot Widget API Reference

## Overview

The Chatbot Widget provides a powerful browser-based communication API with a comprehensive `ChatArgs` initialization system and namespace-based theming. It allows host applications to interact with the chatbot through a queue-based instruction system, ensuring reliable communication even before the widget is fully loaded.

## Getting Started

### Basic Integration

```html
<!-- 1. Load the widget script (creates container automatically) -->
<script src="dist/chatbot-widget.iife.js"></script>

<!-- 2. Initialize with ChatArgs -->
<script>
  // Initialize with namespace-based theming
  window.ChatbotWidget.push({
    action: 'init',
    args: {
      namespace: 'green-namespace',      // Theme selection
      layoutMode: 'window',              // Display mode
      visibility: 'minimized',           // Initial state
      topic: 'Customer Support',         // Topic context
      context: {
        contentLocale: 'en-US',
        variables: {
          name: '<PERSON>',
          event: 'support_request'
        }
      }
    }
  });
  
  // Listen to events
  window.ChatbotWidget.push({
    action: 'on',
    args: {
      event: 'widget.opened',
      callback: (state) => console.log('Widget opened!', state)
    }
  });
</script>
```

## API Structure

The API uses a queue-based instruction system similar to Google Analytics or other third-party widgets:

```typescript
window.ChatbotWidget.push(instruction: ChatbotInstruction)
```

## ChatArgs API Reference

### ChatArgs Type Definition

The new `ChatArgs` provides comprehensive initialization options:

```typescript
export type ChatArgs = {
  namespace?: 'green-namespace' | 'blue-namespace' | 'purple-namespace' | 'light-namespace' | 'dark-namespace';
  layoutMode?: 'window' | 'inline' | null;
  visibility?: 'open' | 'minimized' | 'hidden' | null;
  parentElement?: HTMLDivElement;
  topic?: string;
  askText?: string;
  sendTopicAndAskText?: boolean;
  fallbackMessage?: string;
  hideOnNoUserResponse?: boolean;
  accountId?: string;
  authToken?: string;
  context?: {
    contentLocale?: string;
    userLocale?: string;
    variables?: {
      name?: string;
      event?: string;
    };
  };
  proxyConfig?: {
    proxyUrl: string;
    debug?: boolean;
  };
  api?: {
    getConversationAuthToken: () => string | undefined;
  };
};
```

### Namespace-Theme System

**Available Namespaces**:
- `green-namespace` - Nature-inspired green theme
- `blue-namespace` - Professional blue theme  
- `purple-namespace` - Creative purple theme
- `light-namespace` - Clean light theme
- `dark-namespace` - High contrast dark theme

## Instructions Reference

### 1. Initialize Widget (NEW ChatArgs)

```typescript
window.ChatbotWidget.push({
  action: 'init',
  args: {
    namespace?: 'green-namespace' | 'blue-namespace' | 'purple-namespace' | 'light-namespace' | 'dark-namespace',
    layoutMode?: 'window' | 'inline' | null,
    visibility?: 'open' | 'minimized' | 'hidden' | null,
    parentElement?: HTMLDivElement,
    topic?: string,
    askText?: string,
    sendTopicAndAskText?: boolean,
    fallbackMessage?: string,
    hideOnNoUserResponse?: boolean,
    accountId?: string,
    authToken?: string,
    context?: {
      contentLocale?: string,
      userLocale?: string,
      variables?: {
        name?: string,
        event?: string
      }
    },
    api?: {
      getConversationAuthToken: () => string | undefined
    }
  }
});
```

**Example:**
```javascript
window.ChatbotWidget.push({
  action: 'init',
  args: {
    theme: 'dark',
    position: 'left-bottom',
    userInfo: {
      name: 'John Doe',
      email: '<EMAIL>',
      id: 'user-123'
    },
    topic: 'Customer Support'
  }
});
```

### 2. Control Widget Visibility

#### Open Widget
```typescript
window.ChatbotWidget.push({ action: 'open' });
```

#### Close Widget
```typescript
window.ChatbotWidget.push({ action: 'close' });
```

#### Initialize DirectLine Connection
```typescript
// Manually trigger DirectLine connection (optional - auto-triggers on open)
window.ChatbotWidget.push({ action: 'initializeConnection' });
```

### 3. Send Messages

```typescript
window.ChatbotWidget.push({
  action: 'sendMessage',
  args: {
    text: string,
    metadata?: Record<string, unknown>
  }
});
```

**Example:**
```javascript
window.ChatbotWidget.push({
  action: 'sendMessage',
  args: {
    text: 'Hello, I need help with my order',
    metadata: {
      orderId: '12345',
      source: 'support-page'
    }
  }
});
```

### 4. Update Settings

#### Set Variables
```typescript
window.ChatbotWidget.push({
  action: 'setVariable',
  args: {
    key: string,
    value: any
  }
});
```

**Examples:**
```javascript
// Change theme
window.ChatbotWidget.push({
  action: 'setVariable',
  args: { key: 'theme', value: 'dark' }
});

// Change position
window.ChatbotWidget.push({
  action: 'setVariable',
  args: { key: 'position', value: 'left-bottom' }
});

// Set custom variables
window.ChatbotWidget.push({
  action: 'setVariable',
  args: { key: 'customSetting', value: { foo: 'bar' } }
});
```

#### Update User Information
```typescript
window.ChatbotWidget.push({
  action: 'setUserInfo',
  args: {
    userInfo: {
      name?: string,
      email?: string,
      id?: string,
      [key: string]: unknown
    }
  }
});
```

#### Set Topic
```typescript
window.ChatbotWidget.push({
  action: 'setTopic',
  args: {
    topic: string,
    message?: string  // Optional initial message
  }
});
```


### 5. Event Management

#### Listen to Events
```typescript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: string,
    callback: Function
  }
});
```

#### Stop Listening to Events
```typescript
window.ChatbotWidget.push({
  action: 'off',
  args: { event: string }
});
```

### 6. Utility Actions

#### Clear Messages
```typescript
window.ChatbotWidget.push({ action: 'clearMessages' });
```

#### Session Data Management

**Store Session Data:**
```typescript
window.ChatbotWidget.push({
  action: 'setSessionData',
  args: {
    visibility: { window: "minimized" },
    options: { theme: "dark", customSetting: "value" },
    accessToken: "your-bot-framework-access-token",
    conversationUrl: "/api/conversations/your-conversation-id",
    customData: { userPreference: "setting" }
  }
});
```

**Retrieve Session Data:**
```typescript
window.ChatbotWidget.push({
  action: 'getSessionData',
  callback: (sessionData) => {
    const { visibility, options, accessToken, conversationUrl } = sessionData;
    console.log('Current session data:', sessionData);
  }
});
```

**Clear Session:**
```typescript
window.ChatbotWidget.push({ action: 'clearSession' });
```

#### Get Current State
```typescript
window.ChatbotWidget.push({
  action: 'getState',
  callback: (state) => console.log(state)
});
```

#### Destroy Widget
```typescript
window.ChatbotWidget.push({ action: 'destroy' });
```

## Events Reference

### Widget Lifecycle Events

#### widget.initialized
Fired when the widget is fully loaded and ready.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'widget.initialized',
    callback: (state) => {
      console.log('Widget ready!', state);
    }
  }
});
```

#### widget.opened / widget.closed
Fired when the widget is opened or closed.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'widget.opened',
    callback: (state) => {
      console.log('Widget opened', state);
    }
  }
});
```

### Message Events

#### message.sent / message.received
Fired when messages are sent by user or received from bot.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'message.sent',
    callback: (message) => {
      console.log('User sent:', message);
    }
  }
});

window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'message.received',
    callback: (message) => {
      console.log('Bot replied:', message);
    }
  }
});
```

### Session Events

#### session.created
Fired when a new session is created.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'session.created',
    callback: (sessionInfo) => {
      console.log('Session created:', sessionInfo);
    }
  }
});
```

### Typing Events

#### typing.start / typing.stop
Fired when bot typing indicator starts/stops.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'typing.start',
    callback: () => console.log('Bot is typing...')
  }
});
```

### Variable Events

#### variable.set
Fired when a variable is set via setVariable.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'variable.set',
    callback: (data) => {
      console.log('Variable set:', data.key, '=', data.value);
    }
  }
});
```

#### theme.changed
Fired when theme is changed (for backwards compatibility).

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'theme.changed',
    callback: (theme) => {
      console.log('Theme changed to:', theme);
    }
  }
});
```

### Error Events

#### error
Fired when an error occurs.

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'error',
    callback: (error) => {
      console.error('Chatbot error:', error);
    }
  }
});
```

## State Object Reference

The `ChatbotState` object contains:

```typescript
interface ChatbotState {
  isOpen: boolean;           // Widget visibility
  isInitialized: boolean;    // Initialization status
  hasMessages: boolean;      // Has conversation started
  messageCount: number;      // Total message count
  currentTheme: string;      // Active theme name
  position: string;          // Button position
  sessionId?: string;        // Current session ID
}
```

## Message Object Reference

```typescript
interface ChatbotMessage {
  id: string;                    // Unique message ID
  text: string;                  // Message content
  isUser: boolean;               // true for user, false for bot
  timestamp: Date;               // Message timestamp
  metadata?: Record<string, unknown>; // Optional metadata
}
```

## Advanced Usage Examples

### E-commerce Integration

```javascript
// Initialize for product support
window.ChatbotWidget.push({
  action: 'init',
  args: {
    theme: 'blue',
    userInfo: {
      name: 'Customer',
      id: 'cust-456',
      membership: 'premium'
    },
    topic: 'Product Support'
  }
});

// Send context when user views a product
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'widget.opened',
    callback: () => {
      window.ChatbotWidget.push({
        action: 'sendMessage',
        args: {
          text: 'I need help with this product',
          metadata: {
            productId: 'prod-789',
            productName: 'Wireless Headphones',
            price: '$99.99',
            page: 'product-detail'
          }
        }
      });
    }
  }
});
```

### Multi-language Support

```javascript
// Detect user language and set topic accordingly
const userLang = navigator.language.startsWith('es') ? 'Spanish' : 'English';

window.ChatbotWidget.push({
  action: 'init',
  args: {
    topic: `Support (${userLang})`,
    userInfo: {
      language: userLang,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }
});
```

### Analytics Integration

```javascript
// Track widget interactions
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'widget.opened',
    callback: () => {
      // Your analytics code
      gtag('event', 'chatbot_opened');
    }
  }
});

window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'message.sent',
    callback: (message) => {
      gtag('event', 'chatbot_message_sent', {
        message_length: message.text.length
      });
    }
  }
});
```

### Dynamic Theme Switching

```javascript
// Switch theme based on time of day
const hour = new Date().getHours();
const theme = hour >= 18 || hour < 6 ? 'dark' : 'light';

window.ChatbotWidget.push({
  action: 'setVariable',
  args: { key: 'theme', value: theme }
});
```

## Browser Compatibility

- Modern browsers (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- Requires JavaScript enabled
- Uses Shadow DOM for style isolation
- localStorage for session persistence

## Error Handling

Always listen for errors to ensure robust integration:

```javascript
window.ChatbotWidget.push({
  action: 'on',
  args: {
    event: 'error',
    callback: (error) => {
      console.error('Chatbot error:', error);
      // Handle error gracefully
      if (error.code === 'INITIALIZATION_FAILED') {
        // Fallback to contact form
        showContactForm();
      }
    }
  }
});
```

## Migration from Legacy API

If you're using the old direct initialization:

```javascript
// Old way ❌
window.ChatbotWidget.initialize({ theme: 'dark' });

// New way ✅
window.ChatbotWidget.push({
  action: 'init',
  args: { theme: 'dark' }
});
```

The old `initialize` method is still supported for backward compatibility but the new queue-based API is recommended for all new integrations.