# 📚 Review Pulse Chatbot Widget Documentation

Complete documentation for the React-based embeddable chatbot widget with Bot Framework integration.

## 🎯 Overview

A fully encapsulated chatbot widget that can be embedded in any website with:
- **Shadow DOM isolation** - Complete style encapsulation
- **Namespace-based theming** - 5 theme variants via namespace parameter
- **DirectLine WebSocket Streaming** - Real-time Bot Framework communication
- **Suggested Actions** - Interactive buttons from Bot Framework activities
- **Lazy Connection** - DirectLine only connects when user opens chat
- **Session persistence** - 24h localStorage with WebSocket state preservation
- **Comprehensive API** - Queue-based instruction system

## 📖 Documentation Structure

### 🛠️ Development
- **[Development Guide](development/development-guide.md)** - Complete technical development guide
- **[Architecture](development/architecture.md)** - System architecture and design patterns
- **[API Reference](development/api-reference.md)** - Complete API documentation and usage examples

### 🤖 DirectLine Proxy Integration
- **[Proxy Setup Guide](../PROXY-SETUP.md)** - DirectLine proxy server configuration
- **[Bot Framework Server](../directline-proxy-server/)** - Complete DirectLine proxy server implementation
- **[Test Pages](../test.html)** - Bot Framework and Copilot Studio integration testing

## 🚀 Quick Start

### Basic Widget
```bash
npm install
npm run build
```

```html
<!-- Just include the script - no HTML div needed! -->
<script src="dist/chatbot-widget.iife.js"></script>
<script>
  ChatbotWidget.push({
    action: 'init',
    args: {
      namespace: 'blue-namespace',
      context: { variables: { name: 'User' } },
      visibility: 'minimized'
    }
  });
</script>
```

### Bot Framework Testing
```bash
# Terminal 1: Start proxy server
cd directline-proxy-server && npm install && npm start

# Terminal 2: Build widget  
npm run build

# Browser: Open test.html with Bot Framework configuration
```

## 📊 Current Features

- ✅ **Shadow DOM isolation** - Complete style encapsulation
- ✅ **Namespace-based theming** - 5 themes via namespace parameter
- ✅ **DirectLine WebSocket Streaming** - Real-time Bot Framework communication via proxy
- ✅ **Suggested Actions** - Interactive buttons from Bot Framework activities with click handlers
- ✅ **Welcome Messages** - Smart Bot Framework activity handling for new vs existing sessions
- ✅ **Lazy Connection** - DirectLine only connects when user opens chat or manually triggered
- ✅ **Widget Persistence** - Hide/show without unmounting preserves WebSocket
- ✅ **Session persistence** - 24h localStorage with conversation state
- ✅ **Layout flexibility** - Window, inline, visibility controls
- ✅ **Modern UI** - Avatar system with Eva bot avatar
- ✅ **Message Timestamps** - HH:MM format above each message
- ✅ **Comprehensive API** - `initializeConnection`, `open`, `close`, and more
- ✅ **Bot Framework Integration** - Microsoft DirectLine API support via proxy server
- ✅ **DirectLine Proxy Integration** - Secure communication with Copilot Studio backend
- ✅ **Proxy Configuration** - Flexible proxy URL and debug settings in ChatArgs
- ✅ **Clean API** - Single ChatArgs type for initialization with proxy support
- ✅ **Auto Container** - Creates its own div element automatically

## 🎨 Theme System

**Available Themes:**
- `green-namespace` - Green accent theme
- `blue-namespace` - Blue accent theme  
- `purple-namespace` - Purple accent theme
- `light-namespace` - Light professional theme
- `dark-namespace` - Dark mode theme

**Usage:**
```javascript
ChatbotWidget.push({
  action: 'init',
  args: { 
    namespace: 'green-namespace',
    proxyConfig: {
      proxyUrl: 'http://localhost:3001',
      debug: true
    }
  }
});
```

## 📁 Project Structure

```
src/
├── components/          # React UI components (includes SuggestedActions)
├── contexts/           # React contexts (ChatBotProvider for Bot Framework)
├── hooks/              # Custom hooks (useMessageHandler for DirectLine streaming)
├── services/           # Business logic (sessionManager with Bot Framework persistence)
├── theme/              # Design system and tokens
├── types/              # TypeScript type definitions (directLineActivity types)
└── main.tsx            # Entry point and Shadow DOM setup

docs/
├── development/        # Technical documentation
└── README.md           # Main documentation hub

directline-proxy-server/ # Complete DirectLine proxy server with auth
test.html              # Comprehensive testing page with Bot Framework examples
```

## 🔧 Development Commands

```bash
npm run dev      # Development server (http://localhost:5173)
npm run build    # Production build (outputs to dist/)
```

## 📝 Integration Examples

See the documentation files for detailed examples of:
- Basic widget integration
- Bot Framework DirectLine integration  
- Theme customization
- Session management
- Error handling

---

**For detailed technical information, see the individual documentation files in the development/ and bot-framework/ folders.**