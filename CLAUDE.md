# Claude Code Assistant Instructions

## 📚 Project Context

This is a **React Chatbot Widget** project designed to be embedded in any website. The widget uses Shadow DOM for complete style isolation and features a comprehensive design system with theming support.

## 🎯 Project Goals

- **Embeddable Widget**: Can be integrated into any website without conflicts
- **Design System**: Token-based theming with multiple variants
- **Personalization**: User-aware responses and context
- **Professional UI**: Modern, accessible interface

## 🔗 Key Documentation

**Primary Reference**: See `development-guide.md` for comprehensive technical documentation, architecture details, and development workflows.

**API Reference**: See `API-REFERENCE.md` for complete communication API documentation and usage examples.

## 🏗️ Architecture Quick Reference

```
Floating Button (Document Body)
└── Click Handler
    ↓
Shadow DOM Container
├── Theme Context (5 themes: light, dark, blue, green, purple)
├── ProxyBotFrameworkProvider (DirectLine proxy to Copilot Studio)
├── Session Manager (localStorage persistence with empty initial messages)
├── ProxyDirectLineService (secure token management via proxy)
└── UI Components
    ├── Avatar Component (Eva image + user icon)
    ├── MessageTimestamp (HH:MM format)
    ├── Message Interface (with avatars)
    └── Namespace-based Theme System (green, blue, purple, light, dark)
```

## 📁 Project Structure

```
src/
├── api/                    # API communication layer
│   └── chatbot-api.ts
├── components/             # React UI components
│   ├── Avatar.tsx              # Bot avatar (Eva) component
│   ├── ChatbotButton.tsx       # Floating chat button
│   ├── ChatbotWidget.tsx       # Main widget container
│   ├── FormInput.tsx           # Message input with send icon
│   ├── IconButton.tsx          # Reusable icon button component
│   ├── MessageComponent.tsx    # Memoized message renderer
│   ├── MessageTimestamp.tsx    # HH:MM timestamp display
│   ├── SuggestedActions.tsx    # Interactive action buttons
│   └── index.ts
├── constants/              # Application constants
│   └── index.ts
├── contexts/               # React contexts
│   ├── ProxyBotFrameworkProvider.tsx # DirectLine proxy to Copilot Studio
│   ├── ThemeContext.tsx        # Theme management
│   ├── themeContextDefinition.ts # Theme context types
│   ├── chatbotContextDefinition.ts # Chatbot context types
│   └── index.ts                # Context exports
├── hooks/                  # Custom React hooks
│   ├── useChatbot.ts
│   ├── useTheme.ts
│   ├── useMessageHandler.ts     # Unified message handling logic
│   └── index.ts
├── services/               # Business logic services
│   ├── sessionManager.ts       # Session persistence (empty initial messages)
│   ├── proxyDirectLineService.ts # DirectLine proxy communication
│   └── index.ts                # Service exports
├── utils/                  # Utility functions
│   ├── dom.ts                  # DOM manipulation utilities (scrollToBottom, getOverlayPositionStyle)
│   ├── messages.ts             # Message utilities (findLatestBotMessageIndex)
│   ├── session.ts              # Session utilities (checkForExistingSession)
│   ├── theme.ts                # Theme utilities (getThemeFromNamespace)
│   ├── time.ts                 # Time formatting utilities (formatTime)
│   ├── ui.ts                   # UI utilities (createFloatingButton)
│   └── index.ts                # Utility exports
├── theme/                  # Design system
│   ├── themes.ts
│   ├── tokens.ts
│   ├── themeCSS.ts
│   └── index.ts
├── types/                  # TypeScript type definitions
│   ├── api.ts                  # API-related types
│   ├── chatArgs.ts             # NEW: ChatArgs with namespace support
│   ├── chatbot.ts              # Chatbot configuration types
│   ├── message.ts              # Message types
│   ├── session.ts              # Session types
│   ├── ui.ts                   # UI-related types
│   ├── user.ts                 # User types
│   └── index.ts                # Type exports
├── Chatbot.tsx             # Main chat interface with icon buttons
└── main.tsx                # Application entry point
```

## 🛠️ Development Commands

```bash
npm run dev    # Development server (http://localhost:5173)
npm run build  # Production build (outputs to dist/)
```

**Test Pages**: 
- **Vite Dev**: `http://localhost:5173/test.html` - Live development
- **Live Server**: Any static server - Requires `npm run build` first

## ⚙️ Current Features

- ✅ **Shadow DOM isolation** - Complete style encapsulation
- ✅ **5 Theme system** - Light, dark, blue, green, purple with design tokens
- ✅ **Real-time theme switching** - Dynamic CSS regeneration
- ✅ **Personalized greetings** - User info and topic context
- ✅ **Enhanced Session persistence** - 24h localStorage with Bot Framework integration data
- ✅ **Position flexibility** - 4 corner positions for button
- ✅ **Modern UI** - Close button in header, smooth interactions
- ✅ **Avatar System** - Eva bot avatar only (user avatars removed for cleaner UI)
- ✅ **Message Timestamps** - HH:MM format above each message
- ✅ **Modular Structure** - Clean separation of UI, business logic, and types
- ✅ **Live Server Compatible** - Works with any static file server
- ✅ **Queue-based API** - Seamly-style instruction queue for reliable communication
- ✅ **Event System** - Comprehensive event listening and callbacks
- ✅ **State Management** - Real-time widget state tracking
- ✅ **Variable API** - Generic setVariable API for flexible parameter passing
- ✅ **ChatArgs API** - Comprehensive initialization with namespace-based theming
- ✅ **AUTO div Creation** - Widget automatically creates its own container div
- ✅ **Session Data API** - Store/retrieve Bot Framework tokens, conversation URLs, and custom data
- ✅ **DirectLine WebSocket Streaming** - Real-time Bot Framework DirectLine API with proxy architecture
- ✅ **Suggested Actions** - Interactive button elements from Bot Framework activities (only shown on latest bot message)  
- ✅ **Lazy Connection** - DirectLine only connects when user opens chat or manually triggered
- ✅ **Widget Persistence** - Hide/show without unmounting to preserve WebSocket connections
- ✅ **Icon System** - Lucide React icons with tree-shaking for optimal bundle size
- ✅ **Full Screen Mode** - Toggle between sidebar (25% width) and full page modes
- ✅ **Performance Optimized** - React.memo components, batch state updates, minimal re-renders
- ✅ **Complete Message History** - Both user and bot messages persist across sessions
- ✅ **Auto Session Recovery** - Existing sessions auto-initialize with visual indicators

## 🚀 ChatArgs API

**New Initialization Structure**: The widget now uses a comprehensive `ChatArgs` type for initialization:

```typescript
export type ChatArgs = {
  namespace?: 'green-namespace' | 'blue-namespace' | 'purple-namespace' | 'light-namespace' | 'dark-namespace';
  layoutMode?: 'window' | 'inline' | null;
  visibility?: 'open' | 'minimized' | 'hidden' | null;
  parentElement?: HTMLDivElement;
  topic?: string;
  askText?: string;
  sendTopicAndAskText?: boolean;
  fallbackMessage?: string;
  hideOnNoUserResponse?: boolean;
  accountId?: string;
  authToken?: string;
  context?: {
    contentLocale?: string;
    userLocale?: string;
    variables?: { name?: string; event?: string; };
  };
  api?: {
    getConversationAuthToken: () => string | undefined;
  };
};
```

**Usage Example**:
```javascript
// Just include the script - no HTML div needed!
// <script src="dist/chatbot-widget.iife.js"></script>

ChatbotWidget.push({
  action: 'init',
  args: {
    namespace: 'green-namespace',        // Theme selection
    layoutMode: 'window',                // Display mode
    visibility: 'minimized',             // Initial state
    topic: 'Product Support',            // Topic context
    askText: 'How can we help?',         // Custom greeting
    authToken: 'auth_123',               // Authentication
    context: {
      contentLocale: 'en-US',            // Content language
      userLocale: 'en-US',               // User language
      variables: {
        name: 'John Doe',                // User name
        event: 'product_inquiry'         // Context event
      }
    }
  }
});
```

**Namespace-Theme Mapping**:
- `green-namespace` → Green theme with nature-inspired colors
- `blue-namespace` → Blue theme with professional colors
- `purple-namespace` → Purple theme with creative colors  
- `light-namespace` → Light theme with minimal colors
- `dark-namespace` → Dark theme with high contrast

## 🗄️ Enhanced Session Persistence

**NEW: Comprehensive Session Management** for Bot Framework integration:

### Session Data API
```javascript
// Store Bot Framework session data
ChatbotWidget.push({
  action: 'setSessionData',
  args: {
    visibility: { window: "minimized" },
    options: { theme: "dark", customSetting: "value" },
    accessToken: "your-bot-framework-access-token",
    conversationUrl: "/api/conversations/your-conversation-id",
    customData: { userPreference: "value" }
  }
});

// Retrieve session data
ChatbotWidget.push({
  action: 'getSessionData',
  callback: (sessionData) => {
    const { accessToken, conversationUrl, visibility } = sessionData;
    // Use persisted Bot Framework data
  }
});
```

### Auto-Persistence Features
- **DirectLine Integration**: Access tokens and conversation IDs automatically stored
- **Visibility State**: Widget open/closed state persists across sessions
- **Custom Options**: Store any Bot Framework configuration data
- **24h Expiry**: Sessions automatically expire after 24 hours
- **Type Safety**: All session data is properly typed with existing interfaces

## 🎨 Design System

**Location**: `src/theme/` directory

**Tokens Include** (`src/theme/tokens.ts`):
- Colors (primary, surface, text, borders)
- Spacing (4px to 48px scale)
- Typography (fonts, sizes, weights)  
- Shadows, border radius, z-index, transitions

**Themes** (`src/theme/themes.ts`):
- 5 predefined themes: light, dark, blue, green, purple
- Type-safe theme definitions with design tokens

**Theme Application**: Direct CSS color interpolation (not CSS variables due to Shadow DOM compatibility).

## 🔧 Working with This Project

### Adding Features
1. Update TypeScript interfaces in `src/types/` directory
2. Add business logic to `src/services/` directory
3. Create UI components in `src/components/` directory
4. Extend context providers in `src/contexts/` for state management
5. Add CSS styles to `generateThemeCSS()` in `src/theme/themeCSS.ts`
6. Test across all theme variants

### Styling Rules
- **Never hardcode colors** - Always use theme tokens
- **Maintain Shadow DOM isolation** - All styles must be injected
- **Test theme switching** - Verify all 5 themes work correctly
- **Use consistent spacing** - Follow design token scale

### Common Patterns
- **State Management**: Context + Provider pattern (`src/contexts/`)
- **Business Logic**: Service layer pattern (`src/services/`)
- **Type Safety**: Centralized type definitions (`src/types/`)
- **Component Structure**: Modular React components (`src/components/`)
- **Styling**: Theme tokens → CSS generation → Shadow DOM injection  
- **Button Architecture**: Vanilla DOM (document.body) → Click → React (Shadow DOM)
- **Message Layout**: Timestamp + Bot Avatar (no user avatar) + Content wrapper
- **Icon Components**: Lucide React with tree-shaking, IconButton wrapper component
- **Performance**: React.memo optimization, batch state updates, minimal re-renders
- **Layout Modes**: Sidebar (25% width) and full page (100vw - 40px) with smooth transitions
- **Initialization**: `ChatbotWidget.push({ action: 'init', args: params })` API
- **Queue System**: Instructions queued before initialization, processed after
- **Event Handling**: Listen to widget lifecycle, messages, variable changes
- **Session Handling**: Automatic localStorage persistence via SessionManager
- **Asset Loading**: Static files served from public/ folder
- **AUTO Container Creation**: Widget creates its own `<div id="chatbot-widget"></div>` dynamically
- **NEW: Unified Messaging**: Single source of truth for message handling via `useMessageHandler` hook

## 🚨 Important Constraints

1. **No CSS Variables**: Shadow DOM compatibility requires direct color interpolation
2. **Single Build Output**: All dependencies bundled into one IIFE file
3. **Host Page Isolation**: Must not affect or be affected by external styles
4. **Theme Consistency**: All components must support all 5 themes

## 🎯 Testing Priorities

When making changes, always test:
1. **Theme switching works** (5 themes × all UI states)
2. **Shadow DOM isolation** (styles don't leak in/out)
3. **Session persistence** (refresh page, existing sessions auto-load)
4. **Message history** (both user and bot messages load correctly)
5. **WebSocket streaming** (real-time messages via proxy server)
6. **Suggested actions** (interactive buttons work correctly)
7. **Layout modes** (sidebar ↔ full page toggle functionality)
8. **Icon buttons** (send, close, maximize/minimize work properly)
9. **Performance** (no excessive re-renders, smooth scrolling)
10. **Integration API** (initialization parameters work)
11. **Live Server compatibility** (build + static file serving)

## 🔍 Common Tasks

### Adding a New Theme
```typescript
// 1. Add to src/themes.ts
export const newTheme: Theme = {
  name: 'New',
  variant: 'light',
  tokens: { ...baseTokens, colors: { ...customColors } }
};

// 2. Add to themes object
export const themes = { ...existing, new: newTheme };

// 3. Update switch statement in main.tsx
case 'new': theme = newTheme; break;
```

### Modifying UI Components
- Edit components in `src/components/` folder
- Update CSS in `generateThemeCSS()` function in `src/theme/themeCSS.ts`
- Test with all themes using test.html
- Use IconButton component for consistent icon interface
- Ensure React.memo optimization for performance

### Debugging Issues
- **Browser Console**: Check initialization and error logs (optimized - minimal debug output)
- **Shadow DOM**: Inspect in DevTools (look for #shadow-root)
- **Network Tab**: Verify script loading (eva.png, lucide icons, main.tsx/iife.js)
- **localStorage**: Check session data persistence with new format
- **Theme Switching**: Monitor CSS regeneration in console
- **Button Positioning**: Check document.body for floating button with session indicators
- **Avatar Loading**: Verify `/eva.png` network request (bot avatar only)
- **WebSocket**: Monitor proxy server connection and message streaming
- **Message History**: Check message history API calls for existing sessions

## 📋 Code Quality Standards

- **TypeScript**: Strict typing, no `any` types
- **React**: Functional components with hooks
- **CSS**: Design token-based, theme-consistent
- **Architecture**: Context pattern, provider structure
- **Testing**: Manual testing via test.html page

## 💡 Development Tips

- Use `development-guide.md` for detailed technical reference
- Test frequently with `test.html` during development  
- Monitor console logs for initialization feedback
- Inspect Shadow DOM structure in browser DevTools
- Verify theme switching works in real-time

## 🚀 DirectLine Proxy Usage

**Only Communication Method**: The widget exclusively communicates via DirectLine proxy to your Copilot Studio with lazy connection:

### Connection Management
- **Lazy Loading**: DirectLine connection only created when user opens chat
- **Manual Control**: Use `{ action: 'initializeConnection' }` API for manual connection
- **WebSocket Streaming**: Real-time message delivery via WebSocket (not polling)
- **Session Persistence**: Widget hide/show preserves WebSocket connection

```javascript
// Include the script - no HTML div needed!
// <script src="dist/chatbot-widget.iife.js"></script>

ChatbotWidget.push({
  action: 'init',
  args: {
    namespace: 'green-namespace',        // Theme selection
    layoutMode: 'window',                // Display mode
    visibility: 'minimized',             // Initial state
    proxyConfig: {                       // DirectLine proxy configuration
      proxyUrl: 'http://localhost:3001',
      debug: true
    },
    context: {
      contentLocale: 'en-US',            // Content language
      userLocale: 'en-US',               // User language
      variables: {
        name: 'John Doe',                // User name
        event: 'product_inquiry'         // Context event
      }
    }
  }
});
```

### Key API Actions

```javascript
// Manual connection control
ChatbotWidget.push({ action: 'initializeConnection' });

// Open widget (also initializes connection if needed)
ChatbotWidget.push({ action: 'open' });

// Close widget (preserves connection)
ChatbotWidget.push({ action: 'close' });
```

**Key Behavior**:
- ✅ **Lazy Connection** - DirectLine only connects when chat opens or manually triggered
- ✅ **WebSocket Streaming** - Real-time message delivery via native WebSocket
- ✅ **Suggested Actions** - Interactive buttons from Bot Framework activities (only shown on latest bot message)
- ✅ **Smart Session Handling** - New sessions get welcome messages, existing sessions load history
- ✅ **Auto-Recovery** - Existing sessions auto-initialize with green indicator on chat button
- ✅ **Complete Message History** - Both user and bot messages preserved across browser sessions
- ✅ **Performance Optimized** - Batch loading, memoized components, minimal re-renders
- ✅ **Modern UI** - Icon-based interface with full screen/sidebar mode toggle